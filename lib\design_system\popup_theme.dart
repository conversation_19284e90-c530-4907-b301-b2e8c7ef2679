import 'package:flutter/material.dart';
import 'app_theme.dart';

/// PopupTheme widget provides consistent theming across all popup implementations
/// Based on the specification in docs/design_system/popup_theme_specification.md
class PopupTheme extends InheritedWidget {
  final PopupThemeData data;
  
  const PopupTheme({
    Key? key,
    required this.data,
    required Widget child,
  }) : super(key: key, child: child);
  
  /// Access the PopupThemeData from the widget tree
  static PopupThemeData of(BuildContext context) {
    final PopupTheme? theme = 
        context.dependOnInheritedWidgetOfExactType<PopupTheme>();
    return theme?.data ?? PopupThemeData.standard();
  }
  
  @override
  bool updateShouldNotify(PopupTheme oldWidget) => data != oldWidget.data;
}

/// PopupThemeData contains all styling configuration for popups
/// Uses exclusively AppTheme constants - no hardcoded values
class PopupThemeData {
  final double elevation;
  final double borderRadius;
  final Color borderColor;
  final double borderWidth;
  final Color backgroundColor;
  final EdgeInsets padding;
  final List<BoxShadow> shadows;
  final Color? barrierColor;
  final double? maxWidth;
  final double? maxHeight;
  
  const PopupThemeData({
    required this.elevation,
    required this.borderRadius,
    required this.borderColor,
    required this.backgroundColor,
    required this.padding,
    this.borderWidth = AppTheme.borderWidthThin,
    this.shadows = const [],
    this.barrierColor,
    this.maxWidth,
    this.maxHeight,
  });

  /// Standard popup theme - fallback for when no theme is provided
  factory PopupThemeData.standard() => PopupThemeData(
    elevation: 2,
    borderRadius: AppTheme.radiusLg,
    borderColor: AppTheme.accentCopper,
    backgroundColor: AppTheme.white,
    padding: const EdgeInsets.all(AppTheme.spacingMd),
    shadows: const [AppTheme.shadowSm],
  );

  /// AlertDialog theme for critical user decisions and confirmations
  /// Elevation: 4, Copper border, Large padding
  factory PopupThemeData.alertDialog() => PopupThemeData(
    elevation: 4,
    borderRadius: AppTheme.radiusLg,
    borderColor: AppTheme.accentCopper,
    backgroundColor: AppTheme.white,
    padding: const EdgeInsets.all(AppTheme.spacingLg),
    shadows: const [AppTheme.shadowMd],
    barrierColor: AppTheme.black.withOpacity(0.5),
  );

  /// BottomSheet theme for content selection, forms, and filters
  /// Elevation: 8, Top radius only, Extra top padding for drag handle
  factory PopupThemeData.bottomSheet() => PopupThemeData(
    elevation: 8,
    borderRadius: AppTheme.radiusXl,
    borderColor: Colors.transparent,
    backgroundColor: AppTheme.white,
    padding: const EdgeInsets.fromLTRB(
      AppTheme.spacingLg,    // left: 24.0
      AppTheme.spacingXl,    // top: 32.0 (includes drag handle space)
      AppTheme.spacingLg,    // right: 24.0
      AppTheme.spacingLg,    // bottom: 24.0
    ),
    shadows: const [AppTheme.shadowLg],
    borderWidth: 0,
  );

  /// Custom popup theme for tooltips and contextual information
  /// Matches LocalCard styling exactly - Elevation: 2, Copper border
  factory PopupThemeData.customPopup() => PopupThemeData(
    elevation: 2,
    borderRadius: AppTheme.radiusLg,
    borderColor: AppTheme.accentCopper,
    backgroundColor: AppTheme.white,
    padding: const EdgeInsets.all(AppTheme.spacingMd),
    shadows: const [AppTheme.shadowSm],
  );

  /// SnackBar theme for transient messages and notifications
  /// Navy background, white text, minimal elevation
  factory PopupThemeData.snackBar() => PopupThemeData(
    elevation: 1,
    borderRadius: AppTheme.radiusMd,
    borderColor: Colors.transparent,
    backgroundColor: AppTheme.primaryNavy,
    padding: const EdgeInsets.symmetric(
      horizontal: AppTheme.spacingMd,
      vertical: AppTheme.spacingSm,
    ),
    shadows: const [AppTheme.shadowSm],
    borderWidth: 0,
  );

  /// Wide variant for locals screen popups - increased width for local information
  /// Based on customPopup but with increased maxWidth
  factory PopupThemeData.wide() => PopupThemeData(
    elevation: 2,
    borderRadius: AppTheme.radiusLg,
    borderColor: AppTheme.accentCopper,
    backgroundColor: AppTheme.white,
    padding: const EdgeInsets.all(AppTheme.spacingMd),
    shadows: const [AppTheme.shadowSm],
    maxWidth: 400.0, // Wider for local details
  );

  /// Create a BoxDecoration based on this theme
  BoxDecoration get decoration => BoxDecoration(
    color: backgroundColor,
    borderRadius: BorderRadius.circular(borderRadius),
    border: borderWidth > 0 
        ? Border.all(color: borderColor, width: borderWidth)
        : null,
    boxShadow: shadows,
  );

  /// Create a specialized decoration for bottom sheets (top radius only)
  BoxDecoration get bottomSheetDecoration => BoxDecoration(
    color: backgroundColor,
    borderRadius: BorderRadius.vertical(
      top: Radius.circular(borderRadius),
    ),
    border: borderWidth > 0 
        ? Border.all(color: borderColor, width: borderWidth)
        : null,
    boxShadow: shadows,
  );

  /// Get constraints for the popup container
  BoxConstraints get constraints => BoxConstraints(
    maxWidth: maxWidth ?? double.infinity,
    maxHeight: maxHeight ?? double.infinity,
  );
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PopupThemeData &&
          runtimeType == other.runtimeType &&
          elevation == other.elevation &&
          borderRadius == other.borderRadius &&
          borderColor == other.borderColor &&
          borderWidth == other.borderWidth &&
          backgroundColor == other.backgroundColor &&
          padding == other.padding &&
          barrierColor == other.barrierColor &&
          maxWidth == other.maxWidth &&
          maxHeight == other.maxHeight;

  @override
  int get hashCode =>
      elevation.hashCode ^
      borderRadius.hashCode ^
      borderColor.hashCode ^
      borderWidth.hashCode ^
      backgroundColor.hashCode ^
      padding.hashCode ^
      barrierColor.hashCode ^
      maxWidth.hashCode ^
      maxHeight.hashCode;
}

/// AlertDialog theme class with predefined styling constants
class AlertDialogTheme {
  static const BoxDecoration decoration = BoxDecoration(
    color: AppTheme.white,
    borderRadius: BorderRadius.all(
      Radius.circular(AppTheme.radiusLg),
    ),
  );
  
  static const EdgeInsets padding = EdgeInsets.all(AppTheme.spacingLg);
  static const double elevation = 4;
  
  // Title styling
  static final TextStyle titleStyle = AppTheme.headlineSmall;
  
  // Content styling
  static final TextStyle contentStyle = AppTheme.bodyMedium.copyWith(
    color: AppTheme.textSecondary,
  );
  
  // Action button styling
  static final ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: AppTheme.accentCopper,
    foregroundColor: AppTheme.white,
  );

  // Border configuration
  static const BorderSide border = BorderSide(
    color: AppTheme.accentCopper,
    width: AppTheme.borderWidthThin,
  );
}

/// BottomSheet theme class with predefined styling constants
class BottomSheetTheme {
  static const BoxDecoration decoration = BoxDecoration(
    color: AppTheme.white,
    borderRadius: BorderRadius.vertical(
      top: Radius.circular(AppTheme.radiusXl),
    ),
  );
  
  static const EdgeInsets padding = EdgeInsets.fromLTRB(
    AppTheme.spacingLg,
    AppTheme.spacingXl,
    AppTheme.spacingLg,
    AppTheme.spacingLg,
  );
  
  static const double elevation = 8;
  
  // Drag handle widget
  static Widget dragHandle = Container(
    width: 40,
    height: 4,
    margin: EdgeInsets.only(top: AppTheme.spacingMd),
    decoration: BoxDecoration(
      color: AppTheme.lightGray,
      borderRadius: BorderRadius.circular(AppTheme.radiusXs),
    ),
  );
}

/// Custom popup theme class matching LocalCard styling
class CustomPopupTheme {
  static final BoxDecoration decoration = BoxDecoration(
    color: AppTheme.white,
    borderRadius: BorderRadius.circular(AppTheme.radiusLg),
    border: Border.all(
      color: AppTheme.accentCopper,
      width: AppTheme.borderWidthThin,
    ),
  );
  
  static const EdgeInsets padding = EdgeInsets.all(AppTheme.spacingMd);
  static const double elevation = 2;
  
  // Matches LocalCard styling exactly
  static final ShapeBorder shape = RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(AppTheme.radiusLg),
    side: const BorderSide(
      color: AppTheme.accentCopper,
      width: AppTheme.borderWidthThin,
    ),
  );
}

/// SnackBar theme class for notifications and transient messages
class SnackBarTheme {
  static const BoxDecoration decoration = BoxDecoration(
    color: AppTheme.primaryNavy,
    borderRadius: BorderRadius.all(
      Radius.circular(AppTheme.radiusMd),
    ),
  );
  
  static const EdgeInsets padding = EdgeInsets.symmetric(
    horizontal: AppTheme.spacingMd,
    vertical: AppTheme.spacingSm,
  );
  
  static const double elevation = 1;
  
  // Text styling
  static final TextStyle messageStyle = AppTheme.bodyMedium.copyWith(
    color: AppTheme.textOnDark,
  );
  
  // Error variant
  static const BoxDecoration errorDecoration = BoxDecoration(
    color: AppTheme.errorRed,
    borderRadius: BorderRadius.all(
      Radius.circular(AppTheme.radiusMd),
    ),
  );
}

/// Utility extensions for popup theming
extension PopupThemeExtensions on PopupThemeData {
  /// Create a themed container widget
  Widget wrapChild(Widget child) {
    return Container(
      constraints: constraints,
      padding: padding,
      decoration: decoration,
      child: child,
    );
  }

  /// Create a themed Material widget with elevation
  Widget wrapWithMaterial(Widget child) {
    return Material(
      elevation: elevation,
      color: backgroundColor,
      borderRadius: BorderRadius.circular(borderRadius),
      child: Container(
        constraints: constraints,
        padding: padding,
        decoration: borderWidth > 0 
            ? BoxDecoration(
                border: Border.all(color: borderColor, width: borderWidth),
                borderRadius: BorderRadius.circular(borderRadius),
              )
            : null,
        child: child,
      ),
    );
  }
}

/* ============= USAGE EXAMPLES ============= 

// 1. Basic AlertDialog Usage
showDialog(
  context: context,
  builder: (context) => PopupTheme(
    data: PopupThemeData.alertDialog(),
    child: AlertDialog(
      title: Text('Confirm Action'),
      content: Text('Are you sure you want to proceed?'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context, true),
          child: Text('Confirm'),
        ),
      ],
    ),
  ),
);

// 2. Custom Popup with Theme
Widget buildCustomPopup() {
  final theme = PopupThemeData.customPopup();
  return theme.wrapWithMaterial(
    Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text('Local Information', style: AppTheme.headlineSmall),
        SizedBox(height: AppTheme.spacingMd),
        Text('Details here...', style: AppTheme.bodyMedium),
      ],
    ),
  );
}

// 3. Wide Popup for Locals Screen
showDialog(
  context: context,
  builder: (context) => PopupTheme(
    data: PopupThemeData.wide(),
    child: Dialog(
      child: PopupTheme.of(context).wrapChild(
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('IBEW Local 123', style: AppTheme.headlineMedium),
            SizedBox(height: AppTheme.spacingMd),
            // Local details with extra width...
          ],
        ),
      ),
    ),
  ),
);

// 4. BottomSheet with Drag Handle
showModalBottomSheet(
  context: context,
  builder: (context) => PopupTheme(
    data: PopupThemeData.bottomSheet(),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        BottomSheetTheme.dragHandle,
        SizedBox(height: AppTheme.spacingSm),
        Text('Select Option', style: AppTheme.headlineSmall),
        // Content here...
      ],
    ),
  ),
);

// 5. Accessing Theme in Widget Tree
class CustomPopupWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = PopupTheme.of(context);
    return Container(
      decoration: theme.decoration,
      padding: theme.padding,
      child: Text('Themed content'),
    );
  }
}

*/