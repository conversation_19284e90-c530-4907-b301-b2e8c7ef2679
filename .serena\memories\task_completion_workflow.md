# Task Completion Workflow

## Pre-Development Checklist
1. **Read project documentation**:
   - `README.md` for project overview
   - `guide/screens.md` for detailed specifications
   - `CLAUDE.md` for AI assistant guidelines

2. **Check existing tasks**:
   - Look for `TASK.md` to track current tasks
   - Create `TASK.md` if it doesn't exist

3. **Understand electrical theme**:
   - Review electrical components in `lib/electrical_components/`
   - Use Navy (#1A202C) and Copper (#B45309) colors
   - Incorporate electrical symbols and patterns

## Development Process
1. **Code Quality**:
   - Run `flutter analyze` before committing
   - Use `dart format .` for consistent formatting
   - Follow existing code patterns and conventions

2. **Testing Requirements**:
   - Create widget tests for all new screens/components
   - Test structure mirrors `lib/` structure in `test/`
   - Minimum coverage: rendering, interactions, state management, error handling

3. **Firebase Integration**:
   - Never assume Firebase structure - check existing collections
   - Use FlutterFire packages consistently
   - Handle offline scenarios appropriately

## Post-Development Tasks
1. **Testing**:
   ```bash
   flutter test                    # Run all tests
   flutter test test/widgets/      # Widget-specific tests
   flutter analyze                 # Static analysis
   ```

2. **Documentation Updates**:
   - Update `README.md` if adding features
   - Document Firebase schema changes
   - Update `TASK.md` with completion status and any discovered tasks

3. **Code Review Preparation**:
   - Ensure electrical theme consistency
   - Verify mobile-first responsive design
   - Check performance implications
   - Validate accessibility considerations

## Mobile-Specific Considerations
- Test on different screen sizes
- Verify touch target sizes (minimum 44px)
- Check performance with custom painters and animations
- Ensure proper gesture handling
- Validate memory usage with complex diagrams