# Journeyman Jobs - Project Overview

## Purpose
IBEW Mobile Application for Electrical Workers - A comprehensive Flutter app for electrical journeymen, linemen, wiremen, operators, and tree trimmers. Connects skilled electrical workers with job opportunities and storm work, with weather tracking integration.

## Key Features
- Job Board with filtering by classification, location, and type
- Storm Work Hub for emergency power restoration
- Union Directory (797+ IBEW locals)
- Weather Integration (NOAA radar, weather alerts)
- Electrical-themed design with custom components

## Tech Stack
- **Frontend**: Flutter 3.x with null safety
- **State Management**: Provider pattern
- **Backend**: Firebase (Auth, Firestore, Storage)
- **Navigation**: go_router
- **Weather**: NOAA/NWS APIs
- **Maps**: flutter_map with OpenStreetMap
- **Location**: Geolocator

## Development Platform
- Linux system
- Git repository
- Current branch: chore/agp-8-6-upgrade