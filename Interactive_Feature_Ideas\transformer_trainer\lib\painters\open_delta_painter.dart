
import 'package:flutter/material.dart';
import 'base_transformer_painter.dart';
import '../models/transformer_models.dart';

/// Custom painter for Open Delta (V-V) transformer bank configuration
/// Uses only two transformers to provide three-phase service
class OpenDeltaPainter extends BaseTransformerPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    
    // Define transformer positions (only two transformers)
    final t1Position = Offset(centerX, centerY - 40);      // Top transformer
    final t2Position = Offset(centerX, centerY + 40);      // Bottom transformer
    
    // Draw the two transformers
    drawTransformer(canvas, t1Position, 'T1');
    drawTransformer(canvas, t2Position, 'T2');
    
    // Draw missing transformer location (dashed outline)
    _drawMissingTransformer(canvas, Offset(centerX - 80, centerY), 'T3 (Out)');
    
    // Draw primary side (Open Delta configuration)
    _drawPrimaryOpenDelta(canvas, centerX, centerY);
    
    // Draw secondary side (Open Delta configuration)
    _drawSecondaryOpenDelta(canvas, centerX, centerY);
    
    // Draw voltage labels
    _drawVoltageLabels(canvas, centerX, centerY);
    
    // Draw connection terminals
    _drawConnectionTerminals(canvas, centerX, centerY);
    
    // Draw capacity indicator
    _drawCapacityIndicator(canvas, centerX, centerY);
  }

  /// Draw missing transformer with dashed outline
  void _drawMissingTransformer(Canvas canvas, Offset position, String label) {
    const width = 80.0;
    const height = 60.0;
    
    final rect = Rect.fromCenter(
      center: position,
      width: width,
      height: height,
    );

    // Draw dashed outline
    final dashedPaint = Paint()
      ..color = Colors.red[300]!
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    _drawDashedRect(canvas, rect, dashedPaint);

    // Draw X mark to indicate failure/removal
    canvas.drawLine(
      Offset(position.dx - width * 0.3, position.dy - height * 0.3),
      Offset(position.dx + width * 0.3, position.dy + height * 0.3),
      Paint()..color = Colors.red..strokeWidth = 3.0,
    );
    canvas.drawLine(
      Offset(position.dx + width * 0.3, position.dy - height * 0.3),
      Offset(position.dx - width * 0.3, position.dy + height * 0.3),
      Paint()..color = Colors.red..strokeWidth = 3.0,
    );

    // Draw label
    _drawText(canvas, label, position + const Offset(0, -45), 
              TextStyle(color: Colors.red[600], fontSize: 12, fontWeight: FontWeight.bold));
  }

  /// Draw primary Open Delta configuration
  void _drawPrimaryOpenDelta(Canvas canvas, double centerX, double centerY) {
    // Primary input lines
    final phaseAStart = Offset(centerX - 140, centerY - 80);
    final phaseBStart = Offset(centerX - 140, centerY);
    final phaseCStart = Offset(centerX - 140, centerY + 80);
    
    // Primary connection points for open delta (V-V)
    final connectionPoint1 = Offset(centerX - 60, centerY - 40);
    final connectionPoint2 = Offset(centerX - 60, centerY + 40);
    
    // Draw input phase lines
    drawPhaseLine(canvas, phaseAStart, connectionPoint1, 'A');
    drawPhaseLine(canvas, phaseCStart, connectionPoint2, 'C');
    
    // Draw phase B connection to center tap
    final centerTap = Offset(centerX - 60, centerY);
    drawPhaseLine(canvas, phaseBStart, centerTap, 'B');
    
    // Connect transformers to primary points
    final t1H1 = Offset(centerX - 40, centerY - 40);
    final t1H2 = Offset(centerX - 40, centerY - 20);
    final t2H1 = Offset(centerX - 40, centerY + 20);
    final t2H2 = Offset(centerX - 40, centerY + 40);
    
    // Draw V-V primary connections
    canvas.drawLine(connectionPoint1, t1H1, linePaint);
    canvas.drawLine(centerTap, t1H2, linePaint);
    canvas.drawLine(centerTap, t2H1, linePaint);
    canvas.drawLine(connectionPoint2, t2H2, linePaint);
    
    // Draw the "V" shape of the open delta
    canvas.drawLine(connectionPoint1, centerTap, thickLinePaint);
    canvas.drawLine(centerTap, connectionPoint2, thickLinePaint);
  }

  /// Draw secondary Open Delta configuration
  void _drawSecondaryOpenDelta(Canvas canvas, double centerX, double centerY) {
    // Secondary output lines
    final phaseAEnd = Offset(centerX + 140, centerY - 80);
    final phaseBEnd = Offset(centerX + 140, centerY);
    final phaseCEnd = Offset(centerX + 140, centerY + 80);
    
    // Secondary connection points for open delta (V-V)
    final connectionPoint1 = Offset(centerX + 60, centerY - 40);
    final connectionPoint2 = Offset(centerX + 60, centerY + 40);
    final centerTap = Offset(centerX + 60, centerY);
    
    // Draw output phase lines
    drawPhaseLine(canvas, connectionPoint1, phaseAEnd, 'a');
    drawPhaseLine(canvas, centerTap, phaseBEnd, 'b');
    drawPhaseLine(canvas, connectionPoint2, phaseCEnd, 'c');
    
    // Connect transformers to secondary points
    final t1X1 = Offset(centerX + 40, centerY - 40);
    final t1X2 = Offset(centerX + 40, centerY - 20);
    final t2X1 = Offset(centerX + 40, centerY + 20);
    final t2X2 = Offset(centerX + 40, centerY + 40);
    
    // Draw V-V secondary connections
    canvas.drawLine(t1X1, connectionPoint1, linePaint);
    canvas.drawLine(t1X2, centerTap, linePaint);
    canvas.drawLine(t2X1, centerTap, linePaint);
    canvas.drawLine(t2X2, connectionPoint2, linePaint);
    
    // Draw the "V" shape of the open delta
    canvas.drawLine(connectionPoint1, centerTap, thickLinePaint);
    canvas.drawLine(centerTap, connectionPoint2, thickLinePaint);
  }

  /// Draw voltage measurement labels
  void _drawVoltageLabels(Canvas canvas, double centerX, double centerY) {
    // Primary voltage
    _drawText(canvas, '12.47kV', Offset(centerX - 120, centerY - 100), voltageStyle);
    
    // Secondary voltages (note: can be unbalanced under load)
    drawVoltageIndicator(
      canvas,
      Offset(centerX + 140, centerY - 80),
      Offset(centerX + 140, centerY),
      '240V',
    );
    
    drawVoltageIndicator(
      canvas,
      Offset(centerX + 140, centerY),
      Offset(centerX + 140, centerY + 80),
      '240V',
    );
    
    drawVoltageIndicator(
      canvas,
      Offset(centerX + 140, centerY - 80),
      Offset(centerX + 140, centerY + 80),
      '240V*',
    );
    
    // Warning about voltage unbalance
    _drawText(canvas, '*May be unbalanced', 
              Offset(centerX + 50, centerY + 120), 
              TextStyle(color: Colors.orange[600], fontSize: 10, fontStyle: FontStyle.italic));
  }

  /// Draw capacity indicator
  void _drawCapacityIndicator(Canvas canvas, double centerX, double centerY) {
    // Draw capacity warning box
    final warningRect = Rect.fromLTWH(centerX - 100, centerY - 120, 200, 40);
    
    canvas.drawRoundRect(
      warningRect,
      const Radius.circular(6),
      Paint()..color = Colors.orange[50]!,
    );
    
    canvas.drawRoundRect(
      warningRect,
      const Radius.circular(6),
      Paint()
        ..color = Colors.orange[300]!
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke,
    );
    
    // Draw warning icon
    final iconCenter = Offset(centerX - 80, centerY - 100);
    canvas.drawCircle(
      iconCenter,
      8,
      Paint()..color = Colors.orange,
    );
    
    _drawText(canvas, '!', iconCenter - const Offset(2, 5), 
              const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold));
    
    // Draw capacity text
    _drawText(canvas, 'OPEN DELTA OPERATION', 
              Offset(centerX - 50, centerY - 115), 
              const TextStyle(color: Colors.orange, fontSize: 12, fontWeight: FontWeight.bold));
    
    _drawText(canvas, '86.6% of Normal Capacity', 
              Offset(centerX - 50, centerY - 100), 
              const TextStyle(color: Colors.orange, fontSize: 11));
  }

  /// Draw connection terminals
  void _drawConnectionTerminals(Canvas canvas, double centerX, double centerY) {
    // Primary input terminals
    drawTerminal(canvas, Offset(centerX - 140, centerY - 80), 'A', 
                 isInput: true, type: ConnectionType.primary);
    drawTerminal(canvas, Offset(centerX - 140, centerY), 'B', 
                 isInput: true, type: ConnectionType.primary);
    drawTerminal(canvas, Offset(centerX - 140, centerY + 80), 'C', 
                 isInput: true, type: ConnectionType.primary);
    
    // Transformer 1 terminals
    drawTerminal(canvas, Offset(centerX - 40, centerY - 40), 'T1-H1');
    drawTerminal(canvas, Offset(centerX - 40, centerY - 20), 'T1-H2');
    drawTerminal(canvas, Offset(centerX + 40, centerY - 40), 'T1-X1', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 40, centerY - 20), 'T1-X2', 
                 type: ConnectionType.secondary);
    
    // Transformer 2 terminals
    drawTerminal(canvas, Offset(centerX - 40, centerY + 20), 'T2-H1');
    drawTerminal(canvas, Offset(centerX - 40, centerY + 40), 'T2-H2');
    drawTerminal(canvas, Offset(centerX + 40, centerY + 20), 'T2-X1', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 40, centerY + 40), 'T2-X2', 
                 type: ConnectionType.secondary);
    
    // Output terminals
    drawTerminal(canvas, Offset(centerX + 140, centerY - 80), 'a', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 140, centerY), 'b', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 140, centerY + 80), 'c', 
                 type: ConnectionType.secondary);
  }

  /// Helper method to draw dashed rectangle
  void _drawDashedRect(Canvas canvas, Rect rect, Paint paint) {
    const dashWidth = 8.0;
    const dashSpace = 4.0;
    
    // Top edge
    _drawDashedLine(canvas, rect.topLeft, rect.topRight, paint, dashWidth, dashSpace);
    // Right edge
    _drawDashedLine(canvas, rect.topRight, rect.bottomRight, paint, dashWidth, dashSpace);
    // Bottom edge
    _drawDashedLine(canvas, rect.bottomRight, rect.bottomLeft, paint, dashWidth, dashSpace);
    // Left edge
    _drawDashedLine(canvas, rect.bottomLeft, rect.topLeft, paint, dashWidth, dashSpace);
  }

  /// Helper method to draw dashed line with custom dash pattern
  void _drawDashedLine(Canvas canvas, Offset from, Offset to, Paint paint, 
                      double dashWidth, double dashSpace) {
    final distance = (to - from).distance;
    final dashCount = (distance / (dashWidth + dashSpace)).floor();
    
    for (int i = 0; i < dashCount; i++) {
      final start = from + (to - from) * (i * (dashWidth + dashSpace) / distance);
      final end = from + (to - from) * ((i * (dashWidth + dashSpace) + dashWidth) / distance);
      canvas.drawLine(start, end, paint);
    }
  }

  void _drawText(Canvas canvas, String text, Offset position, TextStyle style) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, position);
  }
}
