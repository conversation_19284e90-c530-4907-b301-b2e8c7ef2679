<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Journeyman Jobs</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>journeyman_jobs</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<!-- Notification capabilities -->
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	
	<!-- Permission descriptions -->
	<key>NSUserNotificationAlertStyle</key>
	<string>alert</string>
	
	<!-- Image Picker permissions -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to take photos for your profile picture</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs photo library access to select photos for your profile picture</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs microphone access to record videos</string>
	
	<!-- Location permissions for weather radar -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs location access to show your location on the weather radar and find storm work opportunities near you</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app needs location access to track severe weather in your area and notify you of nearby storm work opportunities</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs location access to track severe weather in your area and notify you of nearby storm work opportunities</string>
</dict>
</plist>
