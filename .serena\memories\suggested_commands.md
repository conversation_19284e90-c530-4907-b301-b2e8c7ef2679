# Suggested Commands for Development

## Flutter Development
```bash
# Install dependencies
flutter pub get

# Run on different platforms
flutter run -d ios          # iOS simulator
flutter run -d android      # Android emulator

# Code analysis and formatting
flutter analyze             # Static analysis
dart format .               # Format code

# Testing
flutter test                # Run unit tests
flutter test test/widgets/  # Run widget tests
flutter test integration_test/ # Run integration tests

# Building for production
flutter build ios --release      # iOS release build
flutter build apk --release     # Android APK
flutter build appbundle --release # Android App Bundle
```

## Development Tools
```bash
# Project navigation
ls -la                      # List files and directories
find . -name "*.dart"       # Find Dart files
grep -r "pattern" lib/      # Search in code

# Git operations
git status                  # Check working tree status
git add .                   # Stage changes
git commit -m "message"     # Commit changes
git push                    # Push to remote
git pull                    # Pull latest changes
```

## Firebase
```bash
# Firebase CLI (if available)
firebase serve              # Local testing
firebase deploy             # Deploy to Firebase
```

## Project Specific
```bash
# Transformer trainer testing
cd Interactive_Feature_Ideas/transformer_trainer
flutter test

# Package testing
flutter packages get        # Install package dependencies
flutter packages pub deps   # Show dependency tree
```

## Performance Monitoring
```bash
# Flutter performance tools
flutter run --profile       # Profile mode
flutter run --release       # Release mode testing
```