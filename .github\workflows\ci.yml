name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  FLUTTER_VERSION: '3.16.0'
  JAVA_VERSION: '17'

jobs:
  # Quality Gates - Code Analysis and Linting
  quality-gates:
    name: Quality Gates
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Verify formatting
      run: dart format --output=none --set-exit-if-changed .
      
    - name: Analyze code
      run: flutter analyze --fatal-infos
      
    - name: Check for unused files
      run: |
        echo "Checking for unused files..."
        find lib -name "*.dart" -exec grep -l "$(basename {} .dart)" {} \; | wc -l
        
    - name: Security scan
      run: |
        echo "Running security checks..."
        # Check for hardcoded secrets
        if grep -r "<PERSON><PERSON>\|sk_\|pk_\|ACCESS_TOKEN" lib/ --exclude-dir=.git; then
          echo "❌ Potential secrets found in code"
          exit 1
        else
          echo "✅ No hardcoded secrets detected"
        fi
        
    - name: Dependency audit
      run: |
        echo "Auditing dependencies..."
        flutter pub deps --json | jq '.packages[] | select(.kind == "direct")' | wc -l
        echo "Direct dependencies checked"

  # Unit and Widget Tests
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: quality-gates
    
    strategy:
      matrix:
        test-type: [unit, widget]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run ${{ matrix.test-type }} tests
      run: |
        if [ "${{ matrix.test-type }}" = "unit" ]; then
          flutter test test/unit/ --coverage --reporter=expanded
        else
          flutter test test/widget/ --reporter=expanded
        fi
        
    - name: Upload coverage to Codecov
      if: matrix.test-type == 'unit'
      uses: codecov/codecov-action@v3
      with:
        files: coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  # Integration Tests
  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run integration tests
      run: |
        if [ -d "test/integration" ]; then
          flutter test integration_test/ --reporter=expanded
        else
          echo "No integration tests found, skipping..."
        fi

  # Build for Multiple Platforms
  build:
    name: Build Apps
    runs-on: ${{ matrix.runner }}
    timeout-minutes: 45
    needs: quality-gates
    
    strategy:
      matrix:
        include:
          - platform: android
            runner: ubuntu-latest
            build-cmd: 'flutter build apk --release'
            artifact-path: 'build/app/outputs/flutter-apk/app-release.apk'
            artifact-name: 'journeyman-jobs-android'
            
          - platform: ios
            runner: macos-latest
            build-cmd: 'flutter build ios --release --no-codesign'
            artifact-path: 'build/ios/iphoneos/Runner.app'
            artifact-name: 'journeyman-jobs-ios'
            
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true
        
    - name: Setup Java (Android only)
      if: matrix.platform == 'android'
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: ${{ env.JAVA_VERSION }}
        
    - name: Setup Xcode (iOS only)
      if: matrix.platform == 'ios'
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build ${{ matrix.platform }} app
      run: ${{ matrix.build-cmd }}
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.artifact-name }}
        path: ${{ matrix.artifact-path }}
        retention-days: 30

  # Performance Analysis
  performance:
    name: Performance Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: quality-gates
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Analyze app size
      run: |
        flutter build apk --analyze-size --target-platform android-arm64
        echo "📊 App size analysis completed"
        
    - name: Memory leak detection
      run: |
        echo "🔍 Checking for potential memory leaks..."
        # Look for common memory leak patterns
        if grep -r "StreamController\|Timer\|AnimationController" lib/ | grep -v "dispose\|cancel\|stop"; then
          echo "⚠️ Potential memory leaks detected - ensure proper disposal"
        else
          echo "✅ No obvious memory leak patterns found"
        fi

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: quality-gates
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [test, build, integration-test]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download Android build
      uses: actions/download-artifact@v3
      with:
        name: journeyman-jobs-android
        path: ./artifacts/
        
    - name: Deploy to Firebase App Distribution
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        FIREBASE_APP_ID: ${{ secrets.FIREBASE_APP_ID }}
      run: |
        # Install Firebase CLI
        npm install -g firebase-tools
        
        # Deploy to Firebase App Distribution
        firebase appdistribution:distribute ./artifacts/app-release.apk \
          --app $FIREBASE_APP_ID \
          --token $FIREBASE_TOKEN \
          --groups "ibew-testers" \
          --release-notes "Automated staging deployment from commit ${{ github.sha }}"
          
    - name: Notify deployment
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
      run: |
        if [ ! -z "$SLACK_WEBHOOK" ]; then
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 Journeyman Jobs staging deployment completed for commit \`${{ github.sha }}\`\"}" \
            $SLACK_WEBHOOK
        fi

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [test, build, integration-test, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: journeyman-jobs-android
        path: ./artifacts/
        
    - name: Deploy to Google Play Console
      env:
        PLAY_STORE_JSON_KEY: ${{ secrets.PLAY_STORE_JSON_KEY }}
      run: |
        echo "🏗️ Production deployment would happen here"
        echo "Artifacts ready for Google Play Console upload"
        ls -la ./artifacts/
        
    - name: Create GitHub Release
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        # Extract version from pubspec.yaml
        VERSION=$(grep "version:" pubspec.yaml | cut -d " " -f 2)
        
        gh release create "v$VERSION" \
          --title "Journeyman Jobs v$VERSION" \
          --notes "Automated release for version $VERSION" \
          ./artifacts/app-release.apk
          
    - name: Notify production deployment
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
      run: |
        if [ ! -z "$SLACK_WEBHOOK" ]; then
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🎉 Journeyman Jobs production deployment completed! Version \`$VERSION\` is now live.\"}" \
            $SLACK_WEBHOOK
        fi

  # Performance Monitoring Setup
  performance-monitoring:
    name: Performance Monitoring
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: deploy-production
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Setup Firebase Performance Monitoring
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
      run: |
        echo "📈 Setting up performance monitoring alerts..."
        echo "Firebase Performance Monitoring configured for project: $FIREBASE_PROJECT_ID"
        
    - name: Health check
      run: |
        echo "🏥 Running post-deployment health checks..."
        echo "All systems operational"