# Development Commands

## Essential Flutter Commands

### Running the App
```bash
flutter run                 # Run in debug mode
flutter run --release       # Run in release mode
flutter run -d ios          # Run on iOS simulator
flutter run -d android      # Run on Android emulator
```

### Dependencies
```bash
flutter pub get             # Install dependencies
flutter pub upgrade         # Upgrade packages
flutter pub outdated        # Check for package updates
```

### Testing
```bash
flutter test                # Run unit tests
flutter test test/widgets/  # Run widget tests
flutter test integration_test/ # Run integration tests
```

### Building
```bash
flutter build apk --release        # Build Android APK
flutter build appbundle --release  # Build Android App Bundle
flutter build ios --release        # Build iOS app
```

### Code Quality
```bash
flutter analyze             # Run static analysis
flutter format .            # Format code
```

### Cleaning
```bash
flutter clean               # Clean build cache
flutter pub cache clean     # Clean pub cache
```

## System Commands (Linux/WSL)
- `ls` - List directory contents
- `cd` - Change directory
- `grep` - Search text patterns
- `find` - Find files
- `git` - Version control operations