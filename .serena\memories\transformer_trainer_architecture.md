# Transformer Trainer Feature Architecture

## Overview
Interactive Flutter widget for training electricians on transformer bank connections. Located in `Interactive_Feature_Ideas/transformer_trainer/`.

## Core Features
- 5 Transformer Bank Types: Wye/Wye, Delta/Delta, Wye/Delta, Delta/Wye, Open Delta
- 2 Learning Modes: Guided (step-by-step) and Quiz (test knowledge)
- Interactive tap/drag connections
- Visual feedback with electrical flash animations
- Multiple difficulty levels (120V, 240V, 480V scenarios)
- Educational content with safety notes

## Architecture Components

### Widgets
- `TransformerTrainer`: Main entry point widget
- `TransformerDiagram`: Interactive diagram with connection points
- `ConnectionPointWidget`: Tap-able connection terminals
- `GuidedModeWidget`: Step-by-step training interface
- `QuizModeWidget`: Testing interface

### State Management
- `TransformerTrainerState`: Provider-based state management
- `TrainingState`: Immutable state model
- Real-time connection validation

### Custom Painters
- `BaseTransformerPainter`: Abstract base for diagram drawing
- Individual painters for each bank type (WyeWyePainter, etc.)
- `ConnectionWirePainter`: Draws wire connections

### Animations
- `FlashAnimationWidget`: Error feedback with lightning bolts
- `SuccessFlashWidget`: Success feedback with check marks
- Pulse animations for selected connection points

### Models
- `ConnectionPoint`: Represents interactive terminals
- `WireConnection`: Connection between two points
- `TrainingStep`: Guided mode instruction steps
- `VoltageScenario`: Difficulty-based voltage configurations