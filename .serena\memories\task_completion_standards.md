# Task Completion Standards

## When a Task is Complete

### Code Quality Checklist
- [ ] All new code follows Flutter/Dart conventions
- [ ] Proper error handling implemented
- [ ] Documentation added for public APIs
- [ ] Electrical theme integration maintained
- [ ] AppTheme constants used consistently

### Testing Requirements
- [ ] Widget tests created for new screens/components
- [ ] Unit tests for business logic
- [ ] Integration tests for user flows
- [ ] All tests passing

### Design Consistency
- [ ] AppTheme color palette used
- [ ] Typography standards followed
- [ ] Spacing system applied
- [ ] Electrical theme elements integrated
- [ ] JJ component prefix used

### Documentation Updates
- [ ] Update README.md if needed
- [ ] Document new Firebase collections/schemas
- [ ] Update CLAUDE.md project instructions if needed
- [ ] Create/update integration guides

### Git Workflow
```bash
git status                  # Check current state
git add .                   # Stage changes
git commit -m "message"     # Commit with descriptive message
git push                    # Push to remote
```

### Final Verification
- [ ] App builds successfully
- [ ] No lint warnings/errors
- [ ] Electrical theme maintained
- [ ] Professional IBEW worker aesthetic preserved
- [ ] Mobile-first responsive design