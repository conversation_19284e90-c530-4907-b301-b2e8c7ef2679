import 'package:flutter/material.dart';
import 'package:journeyman_jobs/design_system/illustrations/electrical_illustrations.dart';
import 'package:journeyman_jobs/design_system/components/reusable_components.dart';

/// Example screen showing how to implement electrical illustrations
class ElectricalIllustrationsExample extends StatelessWidget {
  const ElectricalIllustrationsExample({super.key});

  Widget _buildSection(String title, List<Widget> children, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 16.0), // Replaced AppTheme.spacingMd
        Wrap(
          spacing: 16.0, // Replaced AppTheme.spacingMd
          runSpacing: 16.0, // Replaced AppTheme.spacingMd
          children: children,
        ),
      ],
    );
  }

  Widget _buildIllustrationCard(
    String title,
    ElectricalIllustration illustration,
    String description,
    BuildContext context, {
    Color? color,
  }) {
    return Container(
      width: 160,
      padding: const EdgeInsets.all(16.0), // Replaced AppTheme.spacingMd
      decoration: BoxDecoration(
        color: Colors.white, // Replaced AppTheme.white
        borderRadius: BorderRadius.circular(8.0), // Replaced AppTheme.radiusMd
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          )
        ], // Replaced AppTheme.shadowSm
      ),
      child: Column(
        children: [
          ElectricalIllustrationWidget(
            illustration: illustration,
            width: 80,
            height: 80,
            color: color,
            animate: true,
          ),
          const SizedBox(height: 12.0), // Replaced AppTheme.spacingSm
          Text(
            title,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8.0), // Replaced AppTheme.spacingXs
          Text(
            description,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Electrical Illustrations'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0), // Replaced AppTheme.spacingMd with a fixed value
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section 1: Loading States
            _buildSection(
              'Loading States',
              [
                _buildIllustrationCard(
                  'Circuit Board Loading',
                  ElectricalIllustration.circuitBoard,
                  'Perfect for data processing',
                  context,
                ),
                _buildIllustrationCard(
                  'Job Search Loading',
                  ElectricalIllustration.jobSearch,
                  'For job-related operations',
                  context,
                ),
              ],
              context,
            ),
            
            const SizedBox(height: 32.0), // Replaced AppTheme.spacingXl
            
            // Section 2: Empty States
            _buildSection(
              'Empty States',
              [
                _buildIllustrationCard(
                  'No Results',
                  ElectricalIllustration.noResults,
                  'When search returns empty',
                  context,
                ),
                _buildIllustrationCard(
                  'Light Bulb Ideas',
                  ElectricalIllustration.lightBulb,
                  'For inspiration or tips',
                  context,
                ),
              ],
              context,
            ),
            
            const SizedBox(height: 32.0), // Replaced AppTheme.spacingXl
            
            // Section 3: Success States
            _buildSection(
              'Success & Status',
              [
                _buildIllustrationCard(
                  'Success',
                  ElectricalIllustration.success,
                  'Application submitted',
                  context,
                  color: Colors.green, // Replaced AppTheme.successGreen
                ),
                _buildIllustrationCard(
                  'Maintenance',
                  ElectricalIllustration.maintenance,
                  'System maintenance',
                  context,
                  color: Colors.amber, // Replaced AppTheme.warningYellow
                ),
              ],
              context,
            ),
            
            const SizedBox(height: 32.0), // Replaced AppTheme.spacingXl
            
            // Section 4: Empty State Examples
            Text(
              'Empty State Examples',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16.0), // Replaced AppTheme.spacingMd
            
            // Job search empty state
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300), // Replaced AppTheme.borderLight
                borderRadius: BorderRadius.circular(8.0), // Replaced AppTheme.radiusMd
              ),
              child: const JJEmptyState(
                title: 'No Jobs Found',
                subtitle: 'Try adjusting your search criteria',
                context: 'jobs',
              ),
            ),
            
            const SizedBox(height: 16.0), // Replaced AppTheme.spacingMd
            
            // Saved jobs empty state
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300), // Replaced AppTheme.borderLight
                borderRadius: BorderRadius.circular(8.0), // Replaced AppTheme.radiusMd
              ),
              child: const JJEmptyState(
                title: 'No Saved Jobs',
                subtitle: 'Save jobs to view them here',
                context: 'saved',
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper widget for quick illustration implementation
class QuickElectricalIllustration extends StatelessWidget {
  final ElectricalIllustration illustration;
  final double size;
  final Color? color;

  const QuickElectricalIllustration({
    super.key,
    required this.illustration,
    this.size = 60,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return ElectricalIllustrationWidget(
      illustration: illustration,
      width: size,
      height: size,
      color: color ?? Colors.orange, // Replaced AppTheme.accentCopper
      animate: true,
    );
  }
}

/// Extension for easy context-based illustration selection
extension ContextualIllustrations on String {
  ElectricalIllustration get electricalIllustration {
    return IllustrationHelper.getEmptyStateIllustration(this);
  }
}
