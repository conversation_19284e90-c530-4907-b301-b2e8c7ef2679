
import 'package:flutter/material.dart';

/// Flash animation widget for indicating incorrect connections
class FlashAnimationWidget extends StatelessWidget {
  final AnimationController controller;
  final Color color;

  const FlashAnimationWidget({
    Key? key,
    required this.controller,
    this.color = Colors.red,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: color.withOpacity(0.3 * controller.value),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: CustomPaint(
            painter: <PERSON>BoltPainter(
              progress: controller.value,
              color: color,
            ),
            size: Size.infinite,
          ),
        );
      },
    );
  }
}

/// Custom painter for drawing lightning bolt effect
class <PERSON><PERSON><PERSON>Painter extends CustomPainter {
  final double progress;
  final Color color;

  LightningBoltPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (progress <= 0) return;

    final paint = Paint()
      ..color = color.withOpacity(progress)
      ..strokeWidth = 4.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Draw multiple lightning bolts at random positions
    final random = _SeededRandom(42); // Use seeded random for consistent animation
    
    for (int i = 0; i < 3; i++) {
      final startX = size.width * random.nextDouble();
      final startY = size.height * 0.2;
      final endY = size.height * 0.8;
      
      _drawLightningBolt(canvas, paint, startX, startY, startX + (random.nextDouble() - 0.5) * 100, endY);
    }

    // Draw electric arc effects
    _drawElectricArcs(canvas, paint, size);
  }

  /// Draw a zigzag lightning bolt
  void _drawLightningBolt(Canvas canvas, Paint paint, double startX, double startY, double endX, double endY) {
    final path = Path();
    path.moveTo(startX, startY);

    const segments = 6;
    for (int i = 1; i <= segments; i++) {
      final t = i / segments;
      final x = startX + (endX - startX) * t + (i % 2 == 0 ? 15 : -15) * progress;
      final y = startY + (endY - startY) * t;
      path.lineTo(x, y);
    }

    canvas.drawPath(path, paint);
  }

  /// Draw electric arc effects
  void _drawElectricArcs(Canvas canvas, Paint paint, Size size) {
    final arcPaint = Paint()
      ..color = color.withOpacity(progress * 0.7)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw some curved arcs
    for (int i = 0; i < 2; i++) {
      final centerX = size.width * 0.3 + i * size.width * 0.4;
      final centerY = size.height * 0.5;
      final radius = 30.0 * progress;

      final rect = Rect.fromCircle(
        center: Offset(centerX, centerY),
        radius: radius,
      );

      canvas.drawArc(
        rect,
        0,
        3.14159 * progress * 2,
        false,
        arcPaint,
      );
    }
  }

  @override
  bool shouldRepaint(LightningBoltPainter oldDelegate) {
    return progress != oldDelegate.progress || color != oldDelegate.color;
  }
}

/// Simple seeded random number generator for consistent animations
class _SeededRandom {
  int _seed;

  _SeededRandom(this._seed);

  double nextDouble() {
    _seed = (_seed * 1103515245 + 12345) & 0x7fffffff;
    return _seed / 0x80000000;
  }
}

/// Success flash animation with green color and check marks
class SuccessFlashWidget extends StatelessWidget {
  final AnimationController controller;

  const SuccessFlashWidget({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.2 * controller.value),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: CustomPaint(
            painter: SuccessEffectPainter(
              progress: controller.value,
            ),
            size: Size.infinite,
          ),
        );
      },
    );
  }
}

/// Custom painter for success effects (check marks and sparkles)
class SuccessEffectPainter extends CustomPainter {
  final double progress;

  SuccessEffectPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    if (progress <= 0) return;

    final paint = Paint()
      ..color = Colors.green.withOpacity(progress)
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Draw check mark
    _drawCheckMark(canvas, paint, size);

    // Draw sparkle effects
    _drawSparkles(canvas, paint, size);
  }

  /// Draw an animated check mark
  void _drawCheckMark(Canvas canvas, Paint paint, Size size) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    const checkSize = 20.0;

    final path = Path();
    
    // Left part of check mark
    if (progress > 0.3) {
      path.moveTo(centerX - checkSize, centerY);
      path.lineTo(centerX - checkSize / 3, centerY + checkSize / 2);
    }
    
    // Right part of check mark
    if (progress > 0.6) {
      path.moveTo(centerX - checkSize / 3, centerY + checkSize / 2);
      path.lineTo(centerX + checkSize, centerY - checkSize / 2);
    }

    canvas.drawPath(path, paint);
  }

  /// Draw sparkle effects around the success indicator
  void _drawSparkles(Canvas canvas, Paint paint, Size size) {
    final sparklePaint = Paint()
      ..color = Colors.green.withOpacity(progress * 0.8)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final random = _SeededRandom(123);
    
    for (int i = 0; i < 6; i++) {
      final x = size.width * random.nextDouble();
      final y = size.height * random.nextDouble();
      final sparkleSize = 8.0 * progress;

      // Draw sparkle as a cross
      canvas.drawLine(
        Offset(x - sparkleSize, y),
        Offset(x + sparkleSize, y),
        sparklePaint,
      );
      canvas.drawLine(
        Offset(x, y - sparkleSize),
        Offset(x, y + sparkleSize),
        sparklePaint,
      );
    }
  }

  @override
  bool shouldRepaint(SuccessEffectPainter oldDelegate) {
    return progress != oldDelegate.progress;
  }
}
