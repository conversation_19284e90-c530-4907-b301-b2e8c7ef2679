# Transformer Trainer Animation Integration Example

## Overview
This document provides a concrete example of how to integrate the advanced animation system from the Interactive_Feature_Ideas transformer trainer into the existing codebase.

## Step-by-Step Integration

### 1. Create Enhanced Animation Components

First, create a new directory for the enhanced animations:

```dart
// lib/electrical_components/animations/enhanced/lightning_flash.dart

import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Enhanced lightning flash animation for electrical feedback
class EnhancedLightningFlash extends StatelessWidget {
  final AnimationController controller;
  final Color color;
  final int boltCount;
  final bool showArcs;
  
  const EnhancedLightningFlash({
    Key? key,
    required this.controller,
    this.color = Colors.red,
    this.boltCount = 3,
    this.showArcs = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: color.withOpacity(0.3 * controller.value),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: CustomPaint(
            painter: _EnhancedLightningPainter(
              progress: controller.value,
              color: color,
              boltCount: boltCount,
              showArcs: showArcs,
            ),
            size: Size.infinite,
          ),
        );
      },
    );
  }
}

class _EnhancedLightningPainter extends CustomPainter {
  final double progress;
  final Color color;
  final int boltCount;
  final bool showArcs;
  
  _EnhancedLightningPainter({
    required this.progress,
    required this.color,
    required this.boltCount,
    required this.showArcs,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    if (progress <= 0) return;
    
    final paint = Paint()
      ..color = color.withOpacity(progress)
      ..strokeWidth = 4.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    // Draw lightning bolts
    final random = _SeededRandom(42);
    for (int i = 0; i < boltCount; i++) {
      _drawLightningBolt(canvas, paint, size, random);
    }
    
    // Draw electric arcs if enabled
    if (showArcs) {
      _drawElectricArcs(canvas, paint, size);
    }
  }
  
  void _drawLightningBolt(Canvas canvas, Paint paint, Size size, _SeededRandom random) {
    final startX = size.width * random.nextDouble();
    final startY = size.height * 0.2;
    final endX = startX + (random.nextDouble() - 0.5) * 100;
    final endY = size.height * 0.8;
    
    final path = Path();
    path.moveTo(startX, startY);
    
    const segments = 6;
    for (int i = 1; i <= segments; i++) {
      final t = i / segments;
      final x = startX + (endX - startX) * t + (i % 2 == 0 ? 15 : -15) * progress;
      final y = startY + (endY - startY) * t;
      path.lineTo(x, y);
    }
    
    canvas.drawPath(path, paint);
  }
  
  void _drawElectricArcs(Canvas canvas, Paint paint, Size size) {
    final arcPaint = Paint()
      ..color = color.withOpacity(progress * 0.7)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    for (int i = 0; i < 2; i++) {
      final centerX = size.width * 0.3 + i * size.width * 0.4;
      final centerY = size.height * 0.5;
      final radius = 30.0 * progress;
      
      final rect = Rect.fromCircle(
        center: Offset(centerX, centerY),
        radius: radius,
      );
      
      canvas.drawArc(
        rect,
        0,
        math.pi * progress * 2,
        false,
        arcPaint,
      );
    }
  }
  
  @override
  bool shouldRepaint(_EnhancedLightningPainter oldDelegate) {
    return progress != oldDelegate.progress || 
           color != oldDelegate.color ||
           boltCount != oldDelegate.boltCount;
  }
}

// Seeded random for consistent animations
class _SeededRandom {
  int _seed;
  
  _SeededRandom(this._seed);
  
  double nextDouble() {
    _seed = (_seed * 1103515245 + 12345) & 0x7fffffff;
    return _seed / 0x80000000;
  }
}
```

### 2. Create Success Animation Component

```dart
// lib/electrical_components/animations/enhanced/success_sparkle.dart

import 'package:flutter/material.dart';

/// Enhanced success animation with sparkles and check mark
class EnhancedSuccessSparkle extends StatelessWidget {
  final AnimationController controller;
  final Color color;
  final bool showSparkles;
  
  const EnhancedSuccessSparkle({
    Key? key,
    required this.controller,
    this.color = Colors.green,
    this.showSparkles = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            color: color.withOpacity(0.2 * controller.value),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: CustomPaint(
            painter: _SuccessEffectPainter(
              progress: controller.value,
              color: color,
              showSparkles: showSparkles,
            ),
            size: Size.infinite,
          ),
        );
      },
    );
  }
}

class _SuccessEffectPainter extends CustomPainter {
  final double progress;
  final Color color;
  final bool showSparkles;
  
  _SuccessEffectPainter({
    required this.progress,
    required this.color,
    required this.showSparkles,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    if (progress <= 0) return;
    
    final paint = Paint()
      ..color = color.withOpacity(progress)
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    _drawCheckMark(canvas, paint, size);
    
    if (showSparkles) {
      _drawSparkles(canvas, paint, size);
    }
  }
  
  void _drawCheckMark(Canvas canvas, Paint paint, Size size) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    const checkSize = 20.0;
    
    final path = Path();
    
    // Animate check mark drawing
    if (progress > 0.3) {
      path.moveTo(centerX - checkSize, centerY);
      path.lineTo(centerX - checkSize / 3, centerY + checkSize / 2);
    }
    
    if (progress > 0.6) {
      path.moveTo(centerX - checkSize / 3, centerY + checkSize / 2);
      path.lineTo(centerX + checkSize, centerY - checkSize / 2);
    }
    
    canvas.drawPath(path, paint);
  }
  
  void _drawSparkles(Canvas canvas, Paint paint, Size size) {
    final sparklePaint = Paint()
      ..color = color.withOpacity(progress * 0.8)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    final random = _SeededRandom(123);
    
    for (int i = 0; i < 6; i++) {
      final x = size.width * random.nextDouble();
      final y = size.height * random.nextDouble();
      final sparkleSize = 8.0 * progress;
      
      // Draw sparkle as a cross
      canvas.drawLine(
        Offset(x - sparkleSize, y),
        Offset(x + sparkleSize, y),
        sparklePaint,
      );
      canvas.drawLine(
        Offset(x, y - sparkleSize),
        Offset(x, y + sparkleSize),
        sparklePaint,
      );
    }
  }
  
  @override
  bool shouldRepaint(_SuccessEffectPainter oldDelegate) {
    return progress != oldDelegate.progress || 
           color != oldDelegate.color;
  }
}
```

### 3. Create Animation Mixin for Easy Integration

```dart
// lib/electrical_components/animations/enhanced/animation_mixin.dart

import 'package:flutter/material.dart';
import 'lightning_flash.dart';
import 'success_sparkle.dart';

/// Mixin to easily add electrical animations to any widget
mixin ElectricalAnimationMixin<T extends StatefulWidget> on State<T>, TickerProviderStateMixin {
  late AnimationController _errorController;
  late AnimationController _successController;
  
  AnimationController get errorController => _errorController;
  AnimationController get successController => _successController;
  
  @override
  void initState() {
    super.initState();
    _errorController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _successController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
  }
  
  @override
  void dispose() {
    _errorController.dispose();
    _successController.dispose();
    super.dispose();
  }
  
  /// Play error animation with lightning effect
  Future<void> playErrorAnimation() async {
    await _errorController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    await _errorController.reverse();
  }
  
  /// Play success animation with sparkles
  Future<void> playSuccessAnimation() async {
    await _successController.forward();
    await Future.delayed(const Duration(milliseconds: 400));
    await _successController.reverse();
  }
  
  /// Build error animation overlay
  Widget buildErrorOverlay({Color color = Colors.red, int boltCount = 3}) {
    return Positioned.fill(
      child: IgnorePointer(
        child: EnhancedLightningFlash(
          controller: _errorController,
          color: color,
          boltCount: boltCount,
        ),
      ),
    );
  }
  
  /// Build success animation overlay
  Widget buildSuccessOverlay({Color color = Colors.green}) {
    return Positioned.fill(
      child: IgnorePointer(
        child: EnhancedSuccessSparkle(
          controller: _successController,
          color: color,
        ),
      ),
    );
  }
}
```

### 4. Integration Example in Existing Screen

```dart
// lib/screens/tools/transformer_training_screen.dart

import 'package:flutter/material.dart';
import '../../design_system/app_theme.dart';
import '../../electrical_components/transformer_trainer/jj_transformer_trainer.dart';
import '../../electrical_components/animations/enhanced/animation_mixin.dart';

class TransformerTrainingScreen extends StatefulWidget {
  const TransformerTrainingScreen({super.key});

  @override
  State<TransformerTrainingScreen> createState() => _TransformerTrainingScreenState();
}

class _TransformerTrainingScreenState extends State<TransformerTrainingScreen> 
    with TickerProviderStateMixin, ElectricalAnimationMixin {
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightGray,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryNavy,
        title: Text(
          'Transformer Bank Training',
          style: AppTheme.headlineMedium.copyWith(
            color: AppTheme.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconTheme: const IconThemeData(color: AppTheme.white),
        elevation: 0,
      ),
      body: Stack(
        children: [
          Container(
            color: AppTheme.lightGray,
            child: JJTransformerTrainer(
              onBankComplete: _handleBankCompletion,
              onError: _handleError,
              onConnectionAttempt: _handleConnectionAttempt,
            ),
          ),
          // Add animation overlays
          buildErrorOverlay(color: Colors.red.shade700),
          buildSuccessOverlay(color: AppTheme.accentCopper),
        ],
      ),
    );
  }
  
  void _handleBankCompletion(TransformerBankType bankType) {
    // Play success animation
    playSuccessAnimation();
    
    // Show completion feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: AppTheme.primaryNavy,
        content: Row(
          children: [
            Icon(
              Icons.electrical_services,
              color: AppTheme.accentCopper,
              size: AppTheme.iconSm,
            ),
            const SizedBox(width: AppTheme.spacingSm),
            Expanded(
              child: Text(
                'Great work! You completed the ${bankType.name} transformer bank.',
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  void _handleError(String error) {
    // Play error animation
    playErrorAnimation();
    
    // Show error feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: Colors.red.shade700,
        content: Row(
          children: [
            const Icon(
              Icons.warning,
              color: AppTheme.white,
              size: AppTheme.iconSm,
            ),
            const SizedBox(width: AppTheme.spacingSm),
            Expanded(
              child: Text(
                error,
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  void _handleConnectionAttempt(bool isCorrect) {
    if (isCorrect) {
      // Quick success flash for correct connections
      successController.forward().then((_) {
        Future.delayed(const Duration(milliseconds: 300), () {
          successController.reverse();
        });
      });
    } else {
      // Quick error flash for incorrect connections
      errorController.forward().then((_) {
        Future.delayed(const Duration(milliseconds: 200), () {
          errorController.reverse();
        });
      });
    }
  }
}
```

### 5. Usage in Other Screens

The animation system can be easily reused in other electrical-themed screens:

```dart
// Example: Using in a circuit breaker screen
class CircuitBreakerScreen extends StatefulWidget {
  @override
  State<CircuitBreakerScreen> createState() => _CircuitBreakerScreenState();
}

class _CircuitBreakerScreenState extends State<CircuitBreakerScreen>
    with TickerProviderStateMixin, ElectricalAnimationMixin {
  
  void _tripBreaker() {
    // Simulate electrical fault with lightning animation
    playErrorAnimation();
    // Handle breaker trip logic
  }
  
  void _resetBreaker() {
    // Show successful reset with sparkle animation
    playSuccessAnimation();
    // Handle breaker reset logic
  }
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Your UI here
        buildErrorOverlay(boltCount: 5), // More bolts for dramatic effect
        buildSuccessOverlay(),
      ],
    );
  }
}
```

## Benefits of This Approach

1. **Reusability**: The animation components can be used across multiple screens
2. **Consistency**: All electrical interactions have the same visual language
3. **Performance**: Animations are optimized with caching and efficient painters
4. **Customization**: Easy to adjust colors, bolt count, and effects
5. **Maintainability**: Centralized animation logic in one place

## Next Steps

1. Test animations on various devices for performance
2. Add haptic feedback to complement visual animations
3. Create additional electrical effects (sparks, arcs, etc.)
4. Document animation guidelines for consistent usage
5. Consider accessibility options (reduced motion)