# Unfucking the Transformer Trainer Training

*Problem*: The transformer trainer training is a mess. It's not clear what the user is supposed to be doing, and the feedback is not helpful

*Solution*: We keep all of the useful and helpful data from the interactive feature ideas directory to integrate it with the existing transformer trainer feature and delete all that remains.

## Step 1: Create a New Implementation Plan

We'll call this plan the "Unfuck Plan". It will be a step-by-step guide that walks the us through the process of refactoring a lot of the app such as the navigation, data references, and naming conventions?? maybe?

## Step 2: Define the Steps

We need to define the steps that the user will go through. For each step, we need to define:

- Clearly identify what is to be removed and what is to be kept.
- Delete everything that is to be removed.
- Decide on where to save the remaining feature
- Combine what was kept from the deleted feature and the feature that was not deleted.
- Refactor the codebase accordingly.

## Step 3: The feature to be deleted

***Interactive_Feature_Ideas/transformer_trainer/***

### To be deleted

### To be kept

- Interactive_Feature_Ideas/uploads

## Step 4: The feature to be kept

***lib\screens\tools***

### To be deleted

### To be kept
