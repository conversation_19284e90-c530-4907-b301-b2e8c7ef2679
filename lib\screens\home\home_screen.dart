import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../design_system/app_theme.dart';
import '../../design_system/popup_theme.dart';
import '../../navigation/app_router.dart';
import '../../providers/app_state_provider.dart';
import '../../models/job_model.dart';
import '../../legacy/flutterflow/schema/jobs_record.dart';
import '../../utils/job_formatting.dart';
import '../../widgets/notification_badge.dart';
import '../settings/support/resources_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppStateProvider>().refreshJobs();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.offWhite,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryNavy,
        elevation: 0,
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                gradient: AppTheme.buttonGradient,
                shape: BoxShape.circle,
              ),
              child: Padding(
                padding: const EdgeInsets.all(6),
                child: Icon(
                  Icons.construction,
                  size: 20,
                  color: AppTheme.white,
                ),
              ),
            ),
            const SizedBox(width: AppTheme.spacingSm),
            Text(
              'Journeyman Jobs',
              style: AppTheme.headlineMedium.copyWith(color: AppTheme.white),
            ),
          ],
        ),
        actions: [
          NotificationBadge(
            iconColor: AppTheme.white,
            showPopupOnTap: false,
            onTap: () {
              context.push(AppRouter.notifications);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => context.read<AppStateProvider>().refreshJobs(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingMd),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Consumer<AppStateProvider>(
                builder: (context, appStateProvider, child) {
                  if (!appStateProvider.isAuthenticated) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome back!',
                          style: AppTheme.headlineMedium.copyWith(
                            color: AppTheme.primaryNavy,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingSm),
                        Text(
                          'Guest User',
                          style: AppTheme.bodyLarge.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ],
                    );
                  }

                  final displayName = appStateProvider.userProfile?.displayName ?? appStateProvider.user?.displayName ?? 'User';
                  final photoUrl = appStateProvider.user?.photoURL;
                  final userInitial = displayName.isNotEmpty ? displayName[0].toUpperCase() : 'U';

                  return Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppTheme.primaryNavy,
                        backgroundImage: photoUrl != null ? NetworkImage(photoUrl) : null,
                        child: photoUrl == null
                            ? Text(
                                userInitial,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : null,
                      ),
                      const SizedBox(width: AppTheme.spacingMd),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome back',
                              style: AppTheme.headlineMedium.copyWith(
                                color: AppTheme.primaryNavy,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: AppTheme.spacingSm),
                            Text(
                              displayName,
                              style: AppTheme.bodyLarge.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),

              const SizedBox(height: AppTheme.spacingLg),

              Text(
                'Quick Actions',
                style: AppTheme.headlineSmall.copyWith(
                  color: AppTheme.primaryNavy,
                ),
              ),
              const SizedBox(height: AppTheme.spacingMd),

              // Use Wrap for responsive layout with 3 buttons
              LayoutBuilder(
                builder: (context, constraints) {
                  // Calculate if we should use 2 or 3 columns based on screen width
                  final buttonWidth = (constraints.maxWidth - AppTheme.spacingMd * 2) / 3;
                  final useThreeColumns = buttonWidth >= 90; // Minimum reasonable button width
                  
                  if (useThreeColumns) {
                    // Three columns for larger screens
                    return Row(
                      children: [
                        Expanded(
                          child: _buildElectricalActionCard(
                            'Electrical calc',
                            Icons.calculate_outlined,
                            () {
                              context.push(AppRouter.electricalCalculators);
                            },
                          ),
                        ),
                        const SizedBox(width: AppTheme.spacingMd),
                        Expanded(
                          child: _buildElectricalActionCard(
                            'Find Jobs',
                            Icons.work_outline,
                            () {
                              context.push(AppRouter.jobs);
                            },
                          ),
                        ),
                        const SizedBox(width: AppTheme.spacingMd),
                        Expanded(
                          child: _buildElectricalActionCard(
                            'Transformer\nTraining',
                            Icons.electrical_services,
                            () {
                              // Navigate to Resources screen with Tools tab selected (index 1)
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const ResourcesScreen(initialTabIndex: 1),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    );
                  } else {
                    // Two rows for smaller screens
                    return Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: _buildElectricalActionCard(
                                'Electrical calc',
                                Icons.calculate_outlined,
                                () {
                                  context.push(AppRouter.electricalCalculators);
                                },
                              ),
                            ),
                            const SizedBox(width: AppTheme.spacingMd),
                            Expanded(
                              child: _buildElectricalActionCard(
                                'Find Jobs',
                                Icons.work_outline,
                                () {
                                  context.push(AppRouter.jobs);
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingMd),
                        Row(
                          children: [
                            Expanded(
                              child: _buildElectricalActionCard(
                                'Transformer Training',
                                Icons.electrical_services,
                                () {
                                  // Navigate to Resources screen with Tools tab selected (index 1)
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const ResourcesScreen(initialTabIndex: 1),
                                    ),
                                  );
                                },
                              ),
                            ),
                            Expanded(
                              child: Container(), // Empty container to maintain grid alignment
                            ),
                          ],
                        ),
                      ],
                    );
                  }
                },
              ),

              const SizedBox(height: AppTheme.spacingLg),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Suggested Jobs',
                    style: AppTheme.headlineSmall.copyWith(
                      color: AppTheme.primaryNavy,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      context.push(AppRouter.jobs);
                    },
                    child: Text(
                      'View All',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.accentCopper,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingMd),

              Consumer<AppStateProvider>(
                builder: (context, appStateProvider, child) {
                  if (appStateProvider.isLoadingJobs) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(AppTheme.spacingLg),
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentCopper),
                        ),
                      ),
                    );
                  }

                  if (appStateProvider.jobsError != null) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(AppTheme.spacingLg),
                        child: Column(
                          children: [
                            Text(
                              'Error loading jobs',
                              style: AppTheme.bodyLarge.copyWith(
                                color: AppTheme.errorRed,
                              ),
                            ),
                            const SizedBox(height: AppTheme.spacingSm),
                            ElevatedButton(
                              onPressed: () => appStateProvider.refreshJobs(),
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  if (appStateProvider.jobs.isEmpty) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(AppTheme.spacingLg),
                        child: Column(
                          children: [
                            Icon(
                              Icons.work_off_outlined,
                              size: 48,
                              color: AppTheme.textSecondary,
                            ),
                            const SizedBox(height: AppTheme.spacingMd),
                            Text(
                              'No jobs available at the moment',
                              style: AppTheme.bodyLarge.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                            ),
                            const SizedBox(height: AppTheme.spacingSm),
                            TextButton(
                              onPressed: () => appStateProvider.refreshJobs(),
                              child: Text(
                                'Refresh',
                                style: AppTheme.bodyMedium.copyWith(
                                  color: AppTheme.accentCopper,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  return Column(
                    children: appStateProvider.jobs.take(5).map((job) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: AppTheme.spacingMd),
                        child: _buildSuggestedJobCard(
                          job: job,
                          onTap: () => _showJobDetailsDialog(context, job),
                        ),
                      );
                    }).toList(),
                  );
                },
              ),

              const SizedBox(height: AppTheme.spacingXxl),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildElectricalActionCard(String title, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(AppTheme.radiusMd),
          boxShadow: [AppTheme.shadowSm],
          border: Border.all(
            color: AppTheme.lightGray,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: AppTheme.primaryNavy,
              size: AppTheme.iconLg,
            ),
            const SizedBox(height: AppTheme.spacingSm),
            Text(
              title,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.primaryNavy,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestedJobCard({
    required dynamic job,
    required VoidCallback onTap,
  }) {
    final jobModel = job is JobsRecord ? _convertJobsRecordToJob(job) : job as Job;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(AppTheme.radiusMd),
          boxShadow: [AppTheme.shadowSm],
          border: Border.all(
            color: AppTheme.accentCopper,
            width: AppTheme.borderWidthThin,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      JobFormatting.formatJobTitle(jobModel.jobTitle ?? jobModel.jobClass ?? jobModel.classification ?? 'General Electrical'),
                      style: AppTheme.bodyLarge.copyWith(
                        color: AppTheme.primaryNavy,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: 'Local: ',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.textSecondary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextSpan(
                            text: jobModel.local?.toString() ?? jobModel.localNumber?.toString() ?? '',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMd),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Contractor: ',
                              style: AppTheme.labelMedium.copyWith(
                                color: AppTheme.textSecondary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            TextSpan(
                              text: jobModel.company,
                              style: AppTheme.labelMedium.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 2),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Location: ',
                              style: AppTheme.labelMedium.copyWith(
                                color: AppTheme.textSecondary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            TextSpan(
                              text: JobFormatting.formatLocation(jobModel.location),
                              style: AppTheme.labelMedium.copyWith(
                                color: AppTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (jobModel.wage != null)
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Wages: ',
                              style: AppTheme.bodyLarge.copyWith(
                                color: AppTheme.primaryNavy,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            TextSpan(
                              text: JobFormatting.formatWage(jobModel.wage),
                              style: AppTheme.bodyLarge.copyWith(
                                color: AppTheme.primaryNavy,
                              ),
                            ),
                          ],
                        ),
                      ),
                    const SizedBox(height: 2),
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: 'Per Diem: ',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.textSecondary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextSpan(
                            text: jobModel.perDiem?.isNotEmpty == true
                                ? jobModel.perDiem
                                : '0',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (jobModel.startDate != null) ...[
              const SizedBox(height: AppTheme.spacingXs),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'Start Date: ',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(
                      text: jobModel.startDate ?? '',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (jobModel.hours != null) ...[
              const SizedBox(height: AppTheme.spacingXs),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'Hours: ',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(
                      text: jobModel.hours?.toString() ?? '',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (jobModel.classification != null) ...[
              const SizedBox(height: AppTheme.spacingXs),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'Classification: ',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(
                      text: jobModel.classification ?? '',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (jobModel.duration != null) ...[
              const SizedBox(height: AppTheme.spacingXs),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'Duration: ',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(
                      text: jobModel.duration ?? '',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (jobModel.typeOfWork != null) ...[
              const SizedBox(height: AppTheme.spacingXs),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'Type of Work: ',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(
                      text: jobModel.typeOfWork ?? '',
                      style: AppTheme.labelSmall.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showJobDetailsDialog(BuildContext context, dynamic job) {
    final jobModel = job is JobsRecord ? _convertJobsRecordToJob(job) : job as Job;
    
    showDialog(
      context: context,
      barrierColor: PopupThemeData.wide().barrierColor ?? Colors.black54,
      builder: (BuildContext context) => PopupTheme(
        data: PopupThemeData.wide(),
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: const EdgeInsets.all(AppTheme.spacingLg),
          child: JobDetailsDialog(job: jobModel),
        ),
      ),
    );
  }


  void _submitJobApplication(Job job) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Application submitted for ${job.classification ?? 'the position'}!',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: AppTheme.primaryNavy,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Job _convertJobsRecordToJob(JobsRecord jobsRecord) {
    return Job(
      id: jobsRecord.reference.id,
      reference: jobsRecord.reference,
      company: jobsRecord.company,
      location: jobsRecord.location,
      classification: jobsRecord.classification,
      local: jobsRecord.local,
      wage: jobsRecord.wage,
      hours: jobsRecord.hours,
      perDiem: jobsRecord.perDiem,
      typeOfWork: jobsRecord.typeOfWork,
      startDate: jobsRecord.startDate,
      duration: jobsRecord.duration,
      jobDescription: jobsRecord.jobDescription,
    );
  }
}

class JobDetailsDialog extends StatelessWidget {
  final Job job;

  const JobDetailsDialog({super.key, required this.job});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        maxWidth: MediaQuery.of(context).size.width * 0.9,
      ),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusXl),
        border: Border.all(
          color: AppTheme.accentCopper,
          width: AppTheme.borderWidthThick,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryNavy.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: AppTheme.accentCopper.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with gradient background
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppTheme.spacingLg),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryNavy,
                  AppTheme.secondaryNavy,
                ],
                stops: [0.0, 1.0],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppTheme.radiusXl),
                topRight: Radius.circular(AppTheme.radiusXl),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppTheme.spacingSm,
                              vertical: AppTheme.spacingXs,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.accentCopper,
                              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                            ),
                            child: Icon(
                              Icons.flash_on,
                              size: AppTheme.iconSm,
                              color: AppTheme.white,
                            ),
                          ),
                          const SizedBox(width: AppTheme.spacingSm),
                          Expanded(
                            child: Text(
                              job.company,
                              style: AppTheme.headlineMedium.copyWith(
                                color: AppTheme.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppTheme.spacingXs),
                      Text(
                        'Local ${job.local?.toString() ?? 'N/A'}',
                        style: AppTheme.bodyLarge.copyWith(
                          color: AppTheme.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: AppTheme.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.close, color: AppTheme.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
              ],
            ),
          ),
          
          // Content area
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.spacingLg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Job details section
                  Text(
                    'Job Details',
                    style: AppTheme.headlineSmall.copyWith(
                      color: AppTheme.primaryNavy,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMd),
                  
                  _buildDetailRow('Classification', job.classification ?? 'N/A'),
                  _buildDetailRow('Location', job.location),
                  _buildDetailRow('Hours', '${job.hours ?? 'N/A'} hours/week'),
                  _buildDetailRow('Wage', job.wage != null ? '\$${job.wage}/hr' : 'N/A'),
                  _buildDetailRow('Per Diem', job.perDiem?.isNotEmpty == true ? 'Yes' : 'No'),
                  _buildDetailRow('Start Date', job.startDate ?? 'N/A'),
                  _buildDetailRow('Duration', job.duration ?? 'N/A'),
                  
                  if (job.jobDescription?.isNotEmpty == true) ...[
                    const SizedBox(height: AppTheme.spacingLg),
                    Text(
                      'Description',
                      style: AppTheme.headlineSmall.copyWith(
                        color: AppTheme.primaryNavy,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMd),
                    Container(
                      padding: const EdgeInsets.all(AppTheme.spacingMd),
                      decoration: BoxDecoration(
                        color: AppTheme.lightGray.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                        border: Border.all(
                          color: AppTheme.accentCopper.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Text(
                        job.jobDescription!,
                        style: AppTheme.bodyMedium,
                      ),
                    ),
                  ],
                  
                  const SizedBox(height: AppTheme.spacingXl),
                  
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            foregroundColor: AppTheme.textSecondary,
                            padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                          ),
                          child: const Text('Close'),
                        ),
                      ),
                      const SizedBox(width: AppTheme.spacingMd),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            // Call the submit application method
                            if (context.findAncestorStateOfType<_HomeScreenState>() != null) {
                              context.findAncestorStateOfType<_HomeScreenState>()!._submitJobApplication(job);
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryNavy,
                            foregroundColor: AppTheme.white,
                            padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                            ),
                          ),
                          child: const Text('Apply'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingXs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTheme.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryNavy,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textDark,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
