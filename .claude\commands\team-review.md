Run these four agent simultaneously in parallel, using --ultrathink, --all-mcp --uc

/spawn --parallel --agent-mobile-developer --agent-ui-ux-designer --agent-flutter-expert --agent-frontend-developer --agent-search-specialist --ultrathink --all-mcp --uc

instructions: Analyze the Journeyman Jobs v3 Flutter app and create comprehensive documentation for the new Transformer Bank feature

*mobile-developer*: Ensure cross-platform compatibility:

- Touch gesture handling for drag-and-drop
- Performance optimization for animations
- Offline capability for training mode
- Screen size adaptations for tablets/phones

*ui-ux-designer*: Design the complete user flow and interface specifications for both Reference and Training modes of the Transformer Bank feature. Create wireframes showing:

- Settings screen integration for "Transformer Bank" option
- Reference mode with interactive transformer diagrams
- Training mode with drag-and-drop interfaces
- Difficulty level selection screens
- Animation states for correct/incorrect wiring
- Visual differentiation between difficulty levels (consider color schemes, animations, complexity indicators)

*flutter-expert*: Review the existing Flutter app structure and define the technical architecture for integrating the Transformer Bank feature including:

- Widget hierarchy for both modes
- State management approach (Provider pattern integration)
- Animation controllers for electrical fire/success animations
- Drag-and-drop implementation strategy
- Data models for transformer configurations

*frontend-developer*: Implement the Transformer Bank feature based on the provided specifications, ensuring:

- Compliance with the app's design theme
- Interactive transformer diagrams in Reference mode
- Drag-and-drop functionality in Training mode
- Difficulty level selection and persistence
- Correct/incorrect wiring animations
- Visual differentiation between difficulty levels

*search-specialest*: Support the other agents by providing real-time up-to-date information from the internet.

- Provide information on any relevant topics that the other agents may ask about.
- Answer any questions that the other agents may have.
- Provide any additional information that the other agents may need.