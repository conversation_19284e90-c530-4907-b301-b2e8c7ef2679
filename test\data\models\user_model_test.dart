import 'package:flutter_test/flutter_test.dart';
import 'package:journeyman_jobs/models/user_model.dart';
import '../../fixtures/mock_data.dart';
import '../../fixtures/test_constants.dart' as Constants;

void main() {
  group('User Model Tests', () {
    test('should create User with required fields', () {
      // Arrange & Act
      final user = UserModel(
        uid: Constants.TestConstants.testUserId,
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '555-1234',
        email: Constants.TestConstants.testEmail,
        address1: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipcode: '12345',
        homeLocal: '123',
        ticketNumber: 'T12345',
        classification: Constants.TestConstants.testClassification,
        isWorking: false,
        constructionTypes: ['Commercial'],
        networkWithOthers: true,
        careerAdvancements: true,
        betterBenefits: true,
        higherPayRate: true,
        learnNewSkill: true,
        travelToNewLocation: true,
        findLongTermWork: true,
        onboardingStatus: 'completed',
        createdTime: DateTime.now(),
      );

      // Assert
      expect(user.uid, equals(Constants.TestConstants.testUserId));
      expect(user.email, equals(Constants.TestConstants.testEmail));
      expect(user.displayName, equals(Constants.TestConstants.testUserName));
    });

    test('should handle IBEW classifications correctly', () {
      // Arrange & Act
      final user = UserModel(
        uid: 'test-uid',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '555-1234',
        email: '<EMAIL>',
        address1: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipcode: '12345',
        homeLocal: '123',
        ticketNumber: 'T12345',
        classification: 'Inside Wireman',
        isWorking: false,
        constructionTypes: ['Commercial'],
        networkWithOthers: true,
        careerAdvancements: true,
        betterBenefits: true,
        higherPayRate: true,
        learnNewSkill: true,
        travelToNewLocation: true,
        findLongTermWork: true,
        onboardingStatus: 'completed',
        createdTime: DateTime.now(),
      );

      // Assert
      expect(user.classification, equals('Inside Wireman'));
      expect(Constants.TestConstants.ibewClassifications, contains(user.classification));
      expect(user.homeLocal, equals('123'));
    });

    test('should store construction types as list', () {
      // Arrange
      final constructionTypes = ['Commercial', 'Industrial', 'Residential'];
      
      // Act
      final user = UserModel(
        uid: 'test-uid',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '555-1234',
        email: '<EMAIL>',
        address1: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipcode: '12345',
        homeLocal: '123',
        ticketNumber: 'T12345',
        classification: 'Inside Wireman',
        isWorking: false,
        constructionTypes: constructionTypes,
        networkWithOthers: true,
        careerAdvancements: true,
        betterBenefits: true,
        higherPayRate: true,
        learnNewSkill: true,
        travelToNewLocation: true,
        findLongTermWork: true,
        onboardingStatus: 'completed',
        createdTime: DateTime.now(),
      );

      // Assert
      expect(user.constructionTypes, equals(constructionTypes));
      expect(user.constructionTypes, hasLength(3));
      expect(user.constructionTypes, contains('Commercial'));
    });

    test('should handle ticket number', () {
      // Arrange & Act
      final user = UserModel(
        uid: 'test-uid',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '555-1234',
        email: '<EMAIL>',
        address1: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipcode: '12345',
        homeLocal: '123',
        ticketNumber: 'T12345',
        classification: 'Inside Wireman',
        isWorking: false,
        constructionTypes: ['Commercial'],
        networkWithOthers: true,
        careerAdvancements: true,
        betterBenefits: true,
        higherPayRate: true,
        learnNewSkill: true,
        travelToNewLocation: true,
        findLongTermWork: true,
        onboardingStatus: 'completed',
        createdTime: DateTime.now(),
      );

      // Assert
      expect(user.ticketNumber, isA<String>());
      expect(user.ticketNumber, equals('T12345'));
    });

    test('should have valid home local', () {
      // Arrange & Act
      final user = UserModel(
        uid: 'test-uid',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '555-1234',
        email: '<EMAIL>',
        address1: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipcode: '12345',
        homeLocal: '456',
        ticketNumber: 'T12345',
        classification: 'Inside Wireman',
        isWorking: false,
        constructionTypes: ['Commercial'],
        networkWithOthers: true,
        careerAdvancements: true,
        betterBenefits: true,
        higherPayRate: true,
        learnNewSkill: true,
        travelToNewLocation: true,
        findLongTermWork: true,
        onboardingStatus: 'completed',
        createdTime: DateTime.now(),
      );

      // Assert
      expect(user.homeLocal, isA<String>());
      expect(user.homeLocal, equals('456'));
    });

    test('should track user creation time', () {
      // Arrange & Act
      final user = MockData.createUser();

      // Assert
      expect(user.createdTime, isA<DateTime>());
      expect(user.createdTime.isBefore(DateTime.now()), isTrue);
    });
  });

  group('User Model IBEW Validation Tests', () {
    test('should validate against real IBEW locals', () {
      // Test with known IBEW locals
      for (final localNumber in TestConstants.commonIBEWLocals.take(5)) {
        // Act
        final user = MockData.createUser(localNumber: localNumber);

        // Assert
        expect(user.localNumber, equals(localNumber));
        expect(TestConstants.commonIBEWLocals, contains(user.localNumber));
      }
    });

    test('should validate electrical classifications', () {
      for (final classification in TestConstants.ibewClassifications) {
        // Act
        final user = MockData.createUser(classification: classification);

        // Assert
        expect(user.classification, equals(classification));
        expect(TestConstants.ibewClassifications, contains(user.classification));
      }
    });
  });
}