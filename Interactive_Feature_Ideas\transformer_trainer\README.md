
# Transformer Bank Connection Trainer

An interactive Flutter widget for training electricians on transformer bank connections. Designed for the Journeyman Jobs app and I.B.E.W. electricians.

## Features

- **5 Transformer Bank Types**: Wye/Wye, Delta/Delta, Wye/Delta, Delta/Wye, Open Delta
- **2 Learning Modes**: Guided (step-by-step) and Quiz (test knowledge)
- **Interactive Connections**: Drag or tap to make connections
- **Visual Feedback**: Electrical flash animations for errors, success animations
- **Multiple Difficulty Levels**: Various voltage scenarios (120V, 240V, 480V)
- **Educational Content**: Explanations, safety notes, common mistakes
- **Offline Ready**: All content stored locally

## Integration

Add to your pubspec.yaml:

```yaml
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1
  flutter_svg: ^2.0.9

assets:
  - packages/transformer_trainer/assets/images/
  - packages/transformer_trainer/assets/animations/
```

## Usage

```dart
import 'package:transformer_trainer/transformer_trainer.dart';

// In your widget
TransformerTrainer(
  initialBankType: TransformerBankType.wyeToWye,
  initialMode: TrainingMode.guided,
  initialDifficulty: DifficultyLevel.beginner,
  onStepComplete: (step) => print('Step completed: $step'),
  onBankComplete: (bankType) => print('Bank completed: $bankType'),
  onError: (error) => print('Error: $error'),
)
```

## File Structure

``` tree
lib/transformer_trainer/
├── transformer_trainer.dart          # Main export
├── widgets/
│   ├── trainer_widget.dart          # Main widget
│   ├── connection_point.dart        # Interactive connection points
│   ├── draggable_wire.dart         # Wire connections
│   └── voltage_label.dart          # Voltage displays
├── models/
│   ├── transformer_models.dart     # Data models & enums
│   └── educational_content.dart    # All educational text
├── modes/
│   ├── guided_mode.dart           # Step-by-step mode
│   └── quiz_mode.dart            # Testing mode
├── painters/
│   ├── base_transformer_painter.dart  # Common painting logic
│   ├── wye_wye_painter.dart          # Wye-Wye configuration
│   ├── delta_delta_painter.dart      # Delta-Delta configuration
│   ├── wye_delta_painter.dart        # Wye-Delta configuration
│   ├── delta_wye_painter.dart        # Delta-Wye configuration
│   └── open_delta_painter.dart       # Open Delta configuration
├── animations/
│   ├── flash_animation.dart        # Error flash effects
│   └── success_animation.dart      # Success animations
└── state/
    └── transformer_state.dart     # State management
```

## Educational Content

The trainer includes comprehensive educational content covering:

- Transformer bank theory and applications
- Safety considerations for each configuration
- Common wiring mistakes and how to avoid them
- Voltage calculations and measurements
- Real-world field applications

## Customization

The widget is designed to be easily customizable:

- Modify voltage levels in `educational_content.dart`
- Add new transformer configurations by extending painters
- Customize animations and visual feedback
- Add additional difficulty scenarios

## License

Designed for Journeyman Jobs app - I.B.E.W. electrical training.
