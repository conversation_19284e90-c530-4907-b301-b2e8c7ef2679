# Transformer Bank Feature - Comprehensive UI/UX Design Specifications

## Executive Summary

This document provides complete UI/UX design specifications for the Transformer Bank feature in Journeyman Jobs v3, creating both Reference and Training modes that serve as professional educational tools for IBEW electrical workers. The design seamlessly integrates with the existing app architecture while introducing innovative training methodologies.

**Key Design Principles:**
- Professional electrical worker aesthetic
- Accessibility-first approach  
- Mobile-optimized responsive design
- Progressive difficulty adaptation
- Electrical-themed visual language

---

## 1. Feature Integration & Navigation Architecture

### 1.1 Settings Screen Integration

**Current Implementation Location:** `/lib/screens/settings/settings_screen.dart`

**Integration Point:** Support section, positioned after "Help & Support"

```dart
// Add to Support section in settings_screen.dart
_MenuOption(
  icon: Icons.transform,
  title: 'Transformer Bank Trainer',
  subtitle: 'Interactive training & reference tool',
  onTap: () => context.push(AppRouter.transformerBank),
  iconColor: AppTheme.accentCopper,
  showChevron: true,
),
```

**Visual Design Standards:**
- Icon: `Icons.transform` with copper accent color
- Consistent with existing menu item styling
- Clear subtitle indicating dual functionality
- Standard chevron indicator for navigation

### 1.2 Router Configuration Updates

**Required Addition to `/lib/navigation/app_router.dart`:**

```dart
class AppRouter {
  // Add new route constants
  static const String transformerBank = '/transformer-bank';
  static const String transformerBankReference = '/transformer-bank/reference';
  static const String transformerBankTraining = '/transformer-bank/training';
  
  // Add to router configuration
  GoRoute(
    path: transformerBank,
    name: 'transformer-bank',
    builder: (context, state) => const TransformerBankHomeScreen(),
    routes: [
      GoRoute(
        path: '/reference',
        name: 'transformer-bank-reference',
        builder: (context, state) => const TransformerBankReferenceScreen(),
        routes: [
          GoRoute(
            path: '/:configType',
            name: 'transformer-diagram',
            builder: (context, state) {
              final configType = state.pathParameters['configType']!;
              return TransformerDiagramScreen(configurationType: configType);
            },
          ),
        ],
      ),
      GoRoute(
        path: '/training',
        name: 'transformer-bank-training',
        builder: (context, state) => const TransformerBankTrainingScreen(),
        routes: [
          GoRoute(
            path: '/:difficulty/:configType',
            name: 'transformer-training-session',
            builder: (context, state) {
              final difficulty = state.pathParameters['difficulty']!;
              final configType = state.pathParameters['configType']!;
              return TransformerTrainingSessionScreen(
                difficulty: difficulty,
                configurationType: configType,
              );
            },
          ),
        ],
      ),
    ],
  ),
```

---

## 2. Screen Architecture & Component Hierarchy

### 2.1 Transformer Bank Home Screen (New)

**Purpose:** Primary landing page for mode selection and feature orientation

**Component Structure:**

```dart
class TransformerBankHomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightGray,
      appBar: _buildAppBar(context),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeroSection(),
            SizedBox(height: AppTheme.spacingXl),
            _buildModeSelectionCards(context),
            SizedBox(height: AppTheme.spacingXl),
            _buildQuickAccessSection(),
          ],
        ),
      ),
    );
  }
}
```

**Hero Section Design:**

```dart
Widget _buildHeroSection() {
  return Container(
    height: 200,
    decoration: BoxDecoration(
      gradient: AppTheme.splashGradient,
      borderRadius: BorderRadius.circular(AppTheme.radiusLg),
      boxShadow: [AppTheme.shadowCard.first],
    ),
    child: Stack(
      children: [
        // Circuit pattern background
        Positioned.fill(
          child: CustomPaint(
            painter: CircuitPatternPainter(
              color: AppTheme.white.withOpacity(0.1),
              density: 0.3,
            ),
          ),
        ),
        // Content overlay
        Padding(
          padding: EdgeInsets.all(AppTheme.spacingLg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.transform,
                    size: AppTheme.iconXl,
                    color: AppTheme.white,
                  ),
                  SizedBox(width: AppTheme.spacingMd),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Master Transformer Banks',
                          style: AppTheme.headlineLarge.copyWith(
                            color: AppTheme.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: AppTheme.spacingSm),
                        Text(
                          'Professional Training & Reference',
                          style: AppTheme.bodyLarge.copyWith(
                            color: AppTheme.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        // Animated lightning effect
        Positioned(
          top: AppTheme.spacingMd,
          right: AppTheme.spacingMd,
          child: ElectricalAnimationWidget(),
        ),
      ],
    ),
  );
}
```

**Mode Selection Cards:**

```dart
Widget _buildModeSelectionCards(BuildContext context) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Choose Your Learning Path',
        style: AppTheme.headlineMedium.copyWith(
          color: AppTheme.textPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      SizedBox(height: AppTheme.spacingMd),
      LayoutBuilder(
        builder: (context, constraints) {
          // Responsive layout: stack on phone, side-by-side on tablet
          if (constraints.maxWidth < 600) {
            return Column(
              children: [
                _buildReferenceModeCard(context),
                SizedBox(height: AppTheme.spacingMd),
                _buildTrainingModeCard(context),
              ],
            );
          } else {
            return Row(
              children: [
                Expanded(child: _buildReferenceModeCard(context)),
                SizedBox(width: AppTheme.spacingMd),
                Expanded(child: _buildTrainingModeCard(context)),
              ],
            );
          }
        },
      ),
    ],
  );
}
```

### 2.2 Reference Mode Screen Architecture

**Configuration Selection Grid:**

```dart
class TransformerBankReferenceScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightGray,
      appBar: AppBar(
        title: Text('Reference Mode'),
        backgroundColor: AppTheme.primaryNavy,
        actions: [
          IconButton(
            icon: Icon(Icons.search),
            onPressed: () => _showConfigurationSearch(context),
          ),
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () => _showReferenceSettings(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInstructionsCard(),
            SizedBox(height: AppTheme.spacingLg),
            _buildConfigurationSections(),
          ],
        ),
      ),
    );
  }
}
```

**Configuration Cards Design:**

```dart
Widget _buildConfigurationCard({
  required TransformerBankType type,
  required String title,
  required String description,
  required List<String> features,
  required VoidCallback onTap,
}) {
  return Card(
    elevation: 2,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppTheme.radiusLg),
    ),
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.radiusLg),
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacingMd),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Configuration diagram preview
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppTheme.lightGray,
                borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              ),
              child: CustomPaint(
                painter: TransformerConfigurationPreviewPainter(type: type),
              ),
            ),
            SizedBox(height: AppTheme.spacingMd),
            
            // Title and description
            Text(
              title,
              style: AppTheme.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            SizedBox(height: AppTheme.spacingSm),
            Text(
              description,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            SizedBox(height: AppTheme.spacingSm),
            
            // Feature list
            ...features.map((feature) => Padding(
              padding: EdgeInsets.only(bottom: AppTheme.spacingXs),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: AppTheme.iconSm,
                    color: AppTheme.successGreen,
                  ),
                  SizedBox(width: AppTheme.spacingSm),
                  Expanded(
                    child: Text(
                      feature,
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
            
            SizedBox(height: AppTheme.spacingMd),
            
            // Action button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: onTap,
                icon: Icon(Icons.visibility, size: AppTheme.iconSm),
                label: Text('View Details'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentCopper,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
```

### 2.3 Interactive Diagram Screen

**Full-Screen Diagram View:**

```dart
class TransformerDiagramScreen extends StatefulWidget {
  final String configurationType;
  
  const TransformerDiagramScreen({
    Key? key,
    required this.configurationType,
  }) : super(key: key);

  @override
  State<TransformerDiagramScreen> createState() => _TransformerDiagramScreenState();
}

class _TransformerDiagramScreenState extends State<TransformerDiagramScreen>
    with TickerProviderStateMixin {
  
  String? selectedComponentId;
  bool isInfoPanelExpanded = false;
  late AnimationController _highlightController;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.white,
      appBar: AppBar(
        title: Text(_getConfigurationDisplayName()),
        backgroundColor: AppTheme.primaryNavy,
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline),
            onPressed: () => _toggleInfoPanel(),
          ),
          IconButton(
            icon: Icon(Icons.share),
            onPressed: () => _shareConfiguration(),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'training',
                child: Row(
                  children: [
                    Icon(Icons.school, color: AppTheme.accentCopper),
                    SizedBox(width: AppTheme.spacingSm),
                    Text('Switch to Training'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print, color: AppTheme.textSecondary),
                    SizedBox(width: AppTheme.spacingSm),
                    Text('Print Diagram'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Main diagram area
          Expanded(
            flex: isInfoPanelExpanded ? 3 : 4,
            child: Container(
              width: double.infinity,
              color: AppTheme.white,
              child: InteractiveTransformerDiagram(
                configurationType: widget.configurationType,
                selectedComponentId: selectedComponentId,
                onComponentTap: _handleComponentTap,
                onComponentHover: _handleComponentHover,
                highlightAnimation: _highlightController,
              ),
            ),
          ),
          
          // Information panel
          AnimatedContainer(
            duration: Duration(milliseconds: 300),
            height: isInfoPanelExpanded ? 250 : 80,
            child: ComponentInformationPanel(
              componentId: selectedComponentId,
              isExpanded: isInfoPanelExpanded,
              onToggle: _toggleInfoPanel,
              onNextComponent: _selectNextComponent,
              onPreviousComponent: _selectPreviousComponent,
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _switchToTrainingMode(),
        backgroundColor: AppTheme.accentCopper,
        icon: Icon(Icons.school),
        label: Text('Start Training'),
      ),
    );
  }
}
```

---

## 3. Difficulty Level Visual Differentiation System

### 3.1 Color Schemes by Difficulty

**Beginner Level (120V/240V Residential):**

```dart
class BeginnerDifficultyTheme {
  static const Color primary = Color(0xFF10B981); // Emerald-500
  static const Color background = Color(0xFFECFDF5); // Emerald-50
  static const Color accent = Color(0xFF059669); // Emerald-600
  static const Color text = Color(0xFF064E3B); // Emerald-900
  
  static const LinearGradient gradient = LinearGradient(
    colors: [Color(0xFF34D399), Color(0xFF10B981)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Animation settings
  static const Duration animationDuration = Duration(milliseconds: 1500);
  static const double componentScale = 1.2; // Larger components
  static const double labelFontSize = 16.0; // Larger text
}
```

**Intermediate Level (240V/480V Commercial):**

```dart
class IntermediateDifficultyTheme {
  static const Color primary = Color(0xFFF59E0B); // Amber-500
  static const Color background = Color(0xFFFFFBEB); // Amber-50
  static const Color accent = Color(0xFFD97706); // Amber-600
  static const Color text = Color(0xFF92400E); // Amber-800
  
  static const LinearGradient gradient = LinearGradient(
    colors: [Color(0xFFFBBF24), Color(0xFFF59E0B)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Animation settings
  static const Duration animationDuration = Duration(milliseconds: 1000);
  static const double componentScale = 1.0; // Standard components
  static const double labelFontSize = 14.0; // Standard text
}
```

**Advanced Level (480V+ Industrial):**

```dart
class AdvancedDifficultyTheme {
  static const Color primary = Color(0xFFDC2626); // Red-600
  static const Color background = Color(0xFFFEF2F2); // Red-50
  static const Color accent = Color(0xFF991B1B); // Red-700
  static const Color text = Color(0xFF7F1D1D); // Red-900
  
  static const LinearGradient gradient = LinearGradient(
    colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Animation settings
  static const Duration animationDuration = Duration(milliseconds: 500);
  static const double componentScale = 0.9; // Compact components
  static const double labelFontSize = 12.0; // Smaller text
}
```

### 3.2 Difficulty Indicator Component

```dart
class JJDifficultyIndicator extends StatelessWidget {
  final DifficultyLevel level;
  final bool showDetails;
  
  const JJDifficultyIndicator({
    Key? key,
    required this.level,
    this.showDetails = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = _getDifficultyTheme(level);
    
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        gradient: theme.gradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        boxShadow: [AppTheme.shadowMd],
      ),
      child: Row(
        children: [
          // Difficulty icon with animated glow
          AnimatedContainer(
            duration: Duration(milliseconds: 800),
            padding: EdgeInsets.all(AppTheme.spacingSm),
            decoration: BoxDecoration(
              color: AppTheme.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusRound),
            ),
            child: Icon(
              _getDifficultyIcon(level),
              size: AppTheme.iconLg,
              color: AppTheme.white,
            ),
          ),
          
          SizedBox(width: AppTheme.spacingMd),
          
          // Difficulty info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      _getDifficultyTitle(level),
                      style: AppTheme.titleLarge.copyWith(
                        color: AppTheme.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: AppTheme.spacingSm),
                    _buildStarRating(level),
                  ],
                ),
                if (showDetails) ...[
                  SizedBox(height: AppTheme.spacingSm),
                  Text(
                    _getDifficultyDescription(level),
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.white.withOpacity(0.9),
                    ),
                  ),
                  SizedBox(height: AppTheme.spacingSm),
                  Text(
                    _getVoltageRange(level),
                    style: AppTheme.labelMedium.copyWith(
                      color: AppTheme.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // Progress indicator (if applicable)
          if (showDetails)
            Column(
              children: [
                CircularProgressIndicator(
                  value: _getUserProgress(level),
                  backgroundColor: AppTheme.white.withOpacity(0.3),
                  valueColor: AlwaysStoppedAnimation<Color>(AppTheme.white),
                ),
                SizedBox(height: AppTheme.spacingSm),
                Text(
                  '${(_getUserProgress(level) * 100).toInt()}%',
                  style: AppTheme.labelSmall.copyWith(
                    color: AppTheme.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
```

---

## 4. Animation Specifications & Visual Feedback

### 4.1 Success Animation System

**Connection Success Animation:**

```dart
class JJConnectionSuccessAnimation extends StatefulWidget {
  final Offset connectionPoint;
  final VoidCallback onComplete;
  
  const JJConnectionSuccessAnimation({
    Key? key,
    required this.connectionPoint,
    required this.onComplete,
  }) : super(key: key);

  @override
  State<JJConnectionSuccessAnimation> createState() => _JJConnectionSuccessAnimationState();
}

class _JJConnectionSuccessAnimationState extends State<JJConnectionSuccessAnimation>
    with TickerProviderStateMixin {
  
  late AnimationController _pulseController;
  late AnimationController _sparkleController;
  late AnimationController _glowController;
  
  late Animation<double> _pulseAnimation;
  late Animation<double> _sparkleAnimation;
  late Animation<double> _glowAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSuccessSequence();
  }
  
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );
    
    _sparkleController = AnimationController(
      duration: Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.elasticOut),
    );
    
    _sparkleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _sparkleController, curve: Curves.easeOut),
    );
    
    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );
  }
  
  void _startSuccessSequence() async {
    // Step 1: Initial pulse
    await _pulseController.forward();
    
    // Step 2: Sparkle effect (overlaps with pulse)
    _sparkleController.forward();
    
    // Step 3: Gentle glow
    await _glowController.forward();
    
    // Complete the animation
    widget.onComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Pulse wave effect
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return CustomPaint(
              painter: PulseWavePainter(
                center: widget.connectionPoint,
                progress: _pulseAnimation.value,
                color: AppTheme.successGreen,
              ),
            );
          },
        ),
        
        // Sparkle particles
        AnimatedBuilder(
          animation: _sparkleAnimation,
          builder: (context, child) {
            return CustomPaint(
              painter: SparkleParticlesPainter(
                center: widget.connectionPoint,
                progress: _sparkleAnimation.value,
                particleCount: 12,
              ),
            );
          },
        ),
        
        // Glow effect
        AnimatedBuilder(
          animation: _glowAnimation,
          builder: (context, child) {
            return CustomPaint(
              painter: ConnectionGlowPainter(
                center: widget.connectionPoint,
                intensity: _glowAnimation.value,
                color: AppTheme.successGreen,
              ),
            );
          },
        ),
      ],
    );
  }
}
```

### 4.2 Error Animation System

**Electrical Fault Animation:**

```dart
class JJElectricalFaultAnimation extends StatefulWidget {
  final Offset faultPoint;
  final VoidCallback onComplete;
  
  const JJElectricalFaultAnimation({
    Key? key,
    required this.faultPoint,
    required this.onComplete,
  }) : super(key: key);

  @override
  State<JJElectricalFaultAnimation> createState() => _JJElectricalFaultAnimationState();
}

class _JJElectricalFaultAnimationState extends State<JJElectricalFaultAnimation>
    with TickerProviderStateMixin {
  
  late AnimationController _flashController;
  late AnimationController _arcController;
  late AnimationController _shakeController;
  
  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _startFaultSequence();
  }
  
  void _startFaultSequence() async {
    // Step 1: Initial flash
    await _flashController.forward();
    
    // Step 2: Electrical arc
    _arcController.forward();
    
    // Step 3: Shake effect
    _shakeController.forward();
    
    // Wait for all animations to complete
    await Future.delayed(Duration(milliseconds: 2000));
    
    widget.onComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Flash effect
        AnimatedBuilder(
          animation: _flashController,
          builder: (context, child) {
            return CustomPaint(
              painter: ElectricalFlashPainter(
                center: widget.faultPoint,
                intensity: _flashController.value,
              ),
            );
          },
        ),
        
        // Arc effect
        AnimatedBuilder(
          animation: _arcController,
          builder: (context, child) {
            return CustomPaint(
              painter: ElectricalArcPainter(
                center: widget.faultPoint,
                progress: _arcController.value,
              ),
            );
          },
        ),
        
        // Shake effect on entire diagram
        AnimatedBuilder(
          animation: _shakeController,
          builder: (context, child) {
            final offset = Offset(
              math.sin(_shakeController.value * math.pi * 10) * 3,
              math.cos(_shakeController.value * math.pi * 8) * 2,
            );
            return Transform.translate(
              offset: offset,
              child: child,
            );
          },
          child: Container(), // This would wrap the entire diagram
        ),
      ],
    );
  }
}
```

---

## 5. Drag & Drop Interface Specifications

### 5.1 Touch-Optimized Wire Connection System

**Primary Interface: Sticky Keys Mode (Mobile-First)**

```dart
class JJWireConnectionInterface extends StatefulWidget {
  final List<ConnectionPoint> connectionPoints;
  final Function(String fromId, String toId) onConnectionAttempt;
  final DifficultyLevel difficulty;
  
  @override
  State<JJWireConnectionInterface> createState() => _JJWireConnectionInterfaceState();
}

class _JJWireConnectionInterfaceState extends State<JJWireConnectionInterface>
    with TickerProviderStateMixin {
  
  String? selectedWireId;
  String? hoveredPointId;
  List<Connection> activeConnections = [];
  
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Stack(
        children: [
          // Base transformer diagram
          CustomPaint(
            painter: TransformerDiagramPainter(
              connections: activeConnections,
              selectedWire: selectedWireId,
              hoveredPoint: hoveredPointId,
              difficulty: widget.difficulty,
            ),
          ),
          
          // Interactive connection points
          ...widget.connectionPoints.map((point) => 
            Positioned(
              left: point.position.dx - 24,
              top: point.position.dy - 24,
              child: JJConnectionPoint(
                point: point,
                isSelected: selectedWireId == point.id,
                isHovered: hoveredPointId == point.id,
                isConnected: _isPointConnected(point.id),
                onTap: () => _handlePointTap(point.id),
                onHover: (isHovering) => _handlePointHover(point.id, isHovering),
                difficulty: widget.difficulty,
              ),
            ),
          ).toList(),
          
          // Wire connections overlay
          CustomPaint(
            painter: WireConnectionsPainter(
              connections: activeConnections,
              selectedWire: selectedWireId,
              animationProgress: _connectionAnimationController.value,
            ),
          ),
          
          // Floating connection indicator
          if (selectedWireId != null)
            _buildConnectionIndicator(),
        ],
      ),
    );
  }
}
```

**Connection Point Component:**

```dart
class JJConnectionPoint extends StatefulWidget {
  final ConnectionPoint point;
  final bool isSelected;
  final bool isHovered;
  final bool isConnected;
  final VoidCallback onTap;
  final Function(bool) onHover;
  final DifficultyLevel difficulty;
  
  @override
  Widget build(BuildContext context) {
    final theme = DifficultyThemeManager.getTheme(difficulty);
    final size = _getPointSize();
    
    return GestureDetector(
      onTap: onTap,
      child: MouseRegion(
        onEnter: (_) => onHover(true),
        onExit: (_) => onHover(false),
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _getPointColor(),
            border: Border.all(
              color: _getBorderColor(),
              width: _getBorderWidth(),
            ),
            boxShadow: _getPointShadow(),
          ),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (point.label.isNotEmpty)
                  Text(
                    point.label,
                    style: _getLabelStyle(),
                    textAlign: TextAlign.center,
                  ),
                if (isConnected)
                  Icon(
                    Icons.check_circle,
                    size: 12,
                    color: AppTheme.successGreen,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  double _getPointSize() {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return 48.0; // Large touch targets
      case DifficultyLevel.intermediate:
        return 40.0; // Standard size
      case DifficultyLevel.advanced:
        return 32.0; // Compact for industrial look
    }
  }
}
```

### 5.2 Alternative: Drag & Drop Mode (Tablet/Desktop)

```dart
class JJDragDropWireInterface extends StatefulWidget {
  // Implementation for larger screens with precise cursor control
  
  @override
  Widget build(BuildContext context) {
    return DragTarget<WireConnection>(
      onAccept: (wire) => _handleWireDrop(wire),
      builder: (context, candidateData, rejectedData) {
        return Container(
          child: Stack(
            children: [
              // Drag feedback overlay
              if (candidateData.isNotEmpty)
                Container(
                  decoration: BoxDecoration(
                    color: AppTheme.successGreen.withOpacity(0.2),
                    border: Border.all(
                      color: AppTheme.successGreen,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  ),
                ),
              
              // Draggable wires
              ...connectionPoints.map((point) => 
                Positioned(
                  left: point.position.dx,
                  top: point.position.dy,
                  child: Draggable<WireConnection>(
                    data: WireConnection(fromPoint: point),
                    feedback: _buildWireFeedback(point),
                    childWhenDragging: _buildGhostWire(point),
                    child: _buildWireEndpoint(point),
                  ),
                ),
              ).toList(),
            ],
          ),
        );
      },
    );
  }
}
```

---

## 6. Responsive Design Implementation

### 6.1 Phone Layout (Primary Target)

**Portrait Mode (< 600dp width):**

```dart
class PhoneLayoutTransformerBank extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Collapsible control panel
        AnimatedContainer(
          duration: Duration(milliseconds: 300),
          height: _isControlPanelExpanded ? 200 : 60,
          child: _buildCompactControlPanel(),
        ),
        
        // Full-width diagram area
        Expanded(
          child: Container(
            width: double.infinity,
            child: _buildResponsiveDiagram(),
          ),
        ),
        
        // Bottom action bar
        Container(
          height: 80,
          child: _buildMobileActionBar(),
        ),
      ],
    );
  }
  
  Widget _buildCompactControlPanel() {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        boxShadow: [AppTheme.shadowSm],
      ),
      child: Column(
        children: [
          // Collapsed state: Quick selectors
          Row(
            children: [
              // Difficulty chip
              _buildDifficultyChip(),
              SizedBox(width: AppTheme.spacingSm),
              
              // Configuration chip
              _buildConfigurationChip(),
              
              Spacer(),
              
              // Expand/collapse button
              IconButton(
                icon: Icon(_isControlPanelExpanded 
                  ? Icons.expand_less 
                  : Icons.expand_more),
                onPressed: _toggleControlPanel,
              ),
            ],
          ),
          
          // Expanded state: Full controls
          if (_isControlPanelExpanded) ...[
            SizedBox(height: AppTheme.spacingMd),
            _buildFullControlsExpanded(),
          ],
        ],
      ),
    );
  }
}
```

**Landscape Mode:**

```dart
class LandscapeLayoutTransformerBank extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Side control panel (30% width)
        Container(
          width: MediaQuery.of(context).size.width * 0.3,
          child: _buildSideControlPanel(),
        ),
        
        // Main diagram area (70% width)
        Expanded(
          child: _buildMainDiagramArea(),
        ),
      ],
    );
  }
}
```

### 6.2 Tablet Layout (≥ 600dp width)

```dart
class TabletLayoutTransformerBank extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Enhanced control panel (25% width)
        Container(
          width: 300,
          child: _buildEnhancedControlPanel(),
        ),
        
        // Diagram area with enhanced interactions (75% width)
        Expanded(
          child: Column(
            children: [
              // Toolbar
              Container(
                height: 60,
                child: _buildDiagramToolbar(),
              ),
              
              // Main diagram with multi-touch support
              Expanded(
                child: _buildTabletDiagramArea(),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
```

### 6.3 Responsive Layout Manager

```dart
class ResponsiveTransformerLayout extends StatelessWidget {
  final Widget child;
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final orientation = MediaQuery.of(context).orientation;
        
        // Determine layout type
        if (width < 600) {
          // Phone layout
          if (orientation == Orientation.portrait) {
            return PhoneLayoutTransformerBank();
          } else {
            return LandscapeLayoutTransformerBank();
          }
        } else {
          // Tablet layout
          return TabletLayoutTransformerBank();
        }
      },
    );
  }
}
```

---

## 7. Accessibility Implementation

### 7.1 Screen Reader Support

**Semantic Annotations:**

```dart
class AccessibleTransformerDiagram extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: 'Interactive transformer bank diagram',
      hint: 'Tap components to learn about their function and specifications',
      child: Stack(
        children: connectionPoints.map((point) => 
          Positioned(
            left: point.position.dx,
            top: point.position.dy,
            child: Semantics(
              label: _getSemanticLabel(point),
              hint: _getSemanticHint(point),
              button: true,
              onTap: () => _announceConnection(point),
              child: JJConnectionPoint(point: point),
            ),
          ),
        ).toList(),
      ),
    );
  }
  
  String _getSemanticLabel(ConnectionPoint point) {
    return '${point.label} connection point, ${point.type.name} terminal, '
           '${point.voltage}V rating, ${_getConnectionStatus(point)}';
  }
  
  String _getSemanticHint(ConnectionPoint point) {
    if (selectedWire != null) {
      return 'Tap to connect ${selectedWire!.label} to ${point.label}';
    } else {
      return 'Tap to select this connection point for wiring';
    }
  }
  
  void _announceConnection(ConnectionPoint point) {
    // Use TTS or screen reader announcements
    SemanticsService.announce(
      'Selected ${point.label} for connection',
      TextDirection.ltr,
    );
  }
}
```

### 7.2 High Contrast Mode

```dart
class HighContrastTransformerTheme {
  static const Color primary = Color(0xFF000000);
  static const Color secondary = Color(0xFFFFFFFF);
  static const Color accent = Color(0xFF0066CC);
  static const Color success = Color(0xFF008000);
  static const Color error = Color(0xFFFF0000);
  static const Color warning = Color(0xFFFFAA00);
  
  static const double enhancedBorderWidth = 3.0;
  static const double enhancedTextSize = 18.0;
  
  static ThemeData get theme {
    return ThemeData(
      // High contrast theme configuration
      colorScheme: ColorScheme.fromSeed(
        seedColor: primary,
        brightness: Brightness.dark,
        primary: primary,
        secondary: accent,
        surface: secondary,
        error: error,
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(
          fontSize: enhancedTextSize,
          fontWeight: FontWeight.bold,
          color: primary,
        ),
      ),
    );
  }
}
```

### 7.3 Motor Impairment Accommodations

```dart
class JJAccessibleConnectionPoint extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Extended tap detection time
      onTap: _handleTap,
      child: Container(
        // Minimum 48dp touch target
        width: math.max(48.0, _getPointSize()),
        height: math.max(48.0, _getPointSize()),
        
        // Visual touch target indicator
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.transparent,
            width: 4,
          ),
        ),
        
        child: Center(
          child: _buildConnectionPoint(),
        ),
      ),
    );
  }
  
  void _handleTap() {
    // Provide haptic feedback
    HapticFeedback.lightImpact();
    
    // Extended visual feedback
    setState(() {
      _showTapFeedback = true;
    });
    
    Timer(Duration(milliseconds: 500), () {
      setState(() {
        _showTapFeedback = false;
      });
    });
    
    // Process the tap
    widget.onTap();
  }
}
```

---

## 8. Performance Optimization Strategies

### 8.1 Efficient Rendering Pipeline

```dart
class OptimizedTransformerDiagram extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: CustomPaint(
        painter: CachedTransformerPainter(
          configuration: widget.configuration,
          cacheKey: _generateCacheKey(),
        ),
        size: Size.infinite,
        isComplex: true,
      ),
    );
  }
}

class CachedTransformerPainter extends CustomPainter {
  static final Map<String, ui.Picture> _paintCache = {};
  
  @override
  void paint(Canvas canvas, Size size) {
    final cacheKey = _generateCacheKey();
    
    ui.Picture? cachedPicture = _paintCache[cacheKey];
    if (cachedPicture == null) {
      // Generate and cache the picture
      final recorder = ui.PictureRecorder();
      final cacheCanvas = Canvas(recorder);
      
      _paintTransformerDiagram(cacheCanvas, size);
      
      cachedPicture = recorder.endRecording();
      _paintCache[cacheKey] = cachedPicture;
    }
    
    // Draw the cached picture
    canvas.drawPicture(cachedPicture);
  }
  
  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return oldDelegate.runtimeType != runtimeType ||
           (oldDelegate as CachedTransformerPainter)._generateCacheKey() != _generateCacheKey();
  }
}
```

### 8.2 Memory Management

```dart
class TransformerAnimationManager with ChangeNotifier {
  final Map<String, AnimationController> _controllers = {};
  final Queue<String> _recentAnimations = Queue();
  static const int maxConcurrentAnimations = 3;
  static const int maxCachedControllers = 10;
  
  AnimationController getController(String id, TickerProvider vsync) {
    // Limit concurrent animations
    if (_controllers.length >= maxConcurrentAnimations) {
      _cleanupOldestController();
    }
    
    if (!_controllers.containsKey(id)) {
      _controllers[id] = AnimationController(
        duration: Duration(milliseconds: 600),
        vsync: vsync,
      );
      
      _controllers[id]!.addStatusListener((status) {
        if (status == AnimationStatus.completed ||
            status == AnimationStatus.dismissed) {
          _markForCleanup(id);
        }
      });
    }
    
    _recentAnimations.add(id);
    return _controllers[id]!;
  }
  
  void _cleanupOldestController() {
    if (_recentAnimations.isNotEmpty) {
      final oldestId = _recentAnimations.removeFirst();
      _controllers[oldestId]?.dispose();
      _controllers.remove(oldestId);
    }
  }
  
  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
    super.dispose();
  }
}
```

---

## 9. Implementation Roadmap

### Phase 1: Foundation (MVP)
**Target: 2 weeks**

1. **Core Infrastructure**
   - New screen components (TransformerBankHomeScreen, etc.)
   - Router integration
   - Settings screen integration
   
2. **Reference Mode**
   - Configuration selection grid
   - Basic interactive diagrams
   - Component information panels
   
3. **Enhanced Training Mode**
   - Visual difficulty differentiation
   - Basic success/error animations
   - Sticky keys connection interface

### Phase 2: Interactions (Enhanced)
**Target: 3 weeks**

1. **Advanced Interactions**
   - Drag & drop interface (tablet)
   - Haptic feedback integration
   - Sound effects system
   
2. **Responsive Design**
   - Phone/tablet layout optimization
   - Landscape mode support
   - Touch target optimization
   
3. **Progress System**
   - Session tracking
   - Performance analytics
   - User achievement system

### Phase 3: Accessibility & Polish (Advanced)
**Target: 2 weeks**

1. **Accessibility Features**
   - Screen reader support
   - High contrast mode
   - Motor impairment accommodations
   
2. **Performance Optimization**
   - Animation pooling
   - Memory management
   - Render caching
   
3. **Advanced Features**
   - Voice navigation
   - Offline mode enhancement
   - Instructor dashboard (future)

---

## 10. Testing & Validation Strategy

### 10.1 Usability Testing Scenarios

**Reference Mode Testing:**
1. First-time user explores Wye-Wye configuration
2. Experienced user quickly looks up component specifications
3. User switches between multiple configurations rapidly

**Training Mode Testing:**
1. Beginner completes first session with guidance
2. Intermediate user recovers from deliberate mistakes
3. Advanced user completes under time pressure

### 10.2 Accessibility Validation

**Screen Reader Testing:**
- Complete navigation using TalkBack/VoiceOver
- Information comprehension without visual elements
- Gesture-based navigation validation

**Motor Impairment Testing:**
- Large touch target effectiveness
- Voice command functionality
- Switch control compatibility

### 10.3 Performance Benchmarks

**Animation Performance:**
- 60 FPS during all animations
- < 16ms frame render time
- < 100MB memory usage peak

**Load Time Targets:**
- < 2s initial screen load
- < 1s configuration switching
- < 500ms interaction response

---

## Conclusion

This comprehensive UI/UX design specification provides a detailed blueprint for implementing the Transformer Bank feature that seamlessly integrates with Journeyman Jobs v3's existing architecture. The design maintains professional electrical worker aesthetics while introducing innovative educational methodologies that will help IBEW members master transformer bank configurations safely and effectively.

The specification emphasizes:
- **Accessibility-first design** ensuring all users can benefit
- **Progressive difficulty adaptation** that grows with user expertise  
- **Mobile-optimized responsive design** for real-world usage
- **Performance optimization** for smooth, professional experience
- **Electrical-themed visual language** that resonates with the target audience

The phased implementation approach allows for iterative development and testing, ensuring each component meets professional standards before advancing to more complex features.