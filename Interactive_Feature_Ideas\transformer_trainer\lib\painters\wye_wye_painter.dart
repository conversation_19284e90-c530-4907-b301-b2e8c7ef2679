
import 'package:flutter/material.dart';
import 'base_transformer_painter.dart';
import '../models/transformer_models.dart';

/// Custom painter for Wye-Wye transformer bank configuration
class WyeWyePainter extends BaseTransformerPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    
    // Define transformer positions (forming a vertical arrangement)
    final t1Position = Offset(centerX, centerY - 80);
    final t2Position = Offset(centerX, centerY);
    final t3Position = Offset(centerX, centerY + 80);
    
    // Draw the three transformers
    drawTransformer(canvas, t1Position, 'T1');
    drawTransformer(canvas, t2Position, 'T2');
    drawTransformer(canvas, t3Position, 'T3');
    
    // Draw primary side (left side - Wye configuration)
    _drawPrimaryWye(canvas, centerX, centerY);
    
    // Draw secondary side (right side - Wye configuration)
    _drawSecondaryWye(canvas, centerX, centerY);
    
    // Draw voltage labels
    _drawVoltageLabels(canvas, centerX, centerY);
    
    // Draw connection terminals
    _drawConnectionTerminals(canvas, centerX, centerY);
  }

  /// Draw primary Wye configuration
  void _drawPrimaryWye(Canvas canvas, double centerX, double centerY) {
    // Primary input lines (left side)
    final phaseAStart = Offset(centerX - 150, centerY - 80);
    final phaseBStart = Offset(centerX - 150, centerY);
    final phaseCStart = Offset(centerX - 150, centerY + 80);
    
    // Primary terminals on transformers
    final t1H1 = Offset(centerX - 40, centerY - 80);
    final t2H1 = Offset(centerX - 40, centerY);
    final t3H1 = Offset(centerX - 40, centerY + 80);
    
    // Draw phase input lines
    drawPhaseLine(canvas, phaseAStart, t1H1, 'A');
    drawPhaseLine(canvas, phaseBStart, t2H1, 'B');
    drawPhaseLine(canvas, phaseCStart, t3H1, 'C');
    
    // Draw primary neutral point (Wye center)
    final primaryNeutral = Offset(centerX - 60, centerY + 40);
    
    // H2 terminals on transformers
    final t1H2 = Offset(centerX - 40, centerY - 60);
    final t2H2 = Offset(centerX - 40, centerY + 20);
    final t3H2 = Offset(centerX - 40, centerY + 100);
    
    // Draw connections from H2 terminals to neutral point
    canvas.drawLine(t1H2, primaryNeutral, linePaint);
    canvas.drawLine(t2H2, primaryNeutral, linePaint);
    canvas.drawLine(t3H2, primaryNeutral, linePaint);
    
    // Draw neutral symbol
    drawNeutralSymbol(canvas, primaryNeutral);
    
    // Draw ground connection for primary neutral
    drawGroundSymbol(canvas, Offset(primaryNeutral.dx, primaryNeutral.dy + 30));
  }

  /// Draw secondary Wye configuration
  void _drawSecondaryWye(Canvas canvas, double centerX, double centerY) {
    // Secondary output lines (right side)
    final phaseAEnd = Offset(centerX + 150, centerY - 80);
    final phaseBEnd = Offset(centerX + 150, centerY);
    final phaseCEnd = Offset(centerX + 150, centerY + 80);
    final neutralEnd = Offset(centerX + 150, centerY + 120);
    
    // Secondary terminals on transformers
    final t1X1 = Offset(centerX + 40, centerY - 80);
    final t2X1 = Offset(centerX + 40, centerY);
    final t3X1 = Offset(centerX + 40, centerY + 80);
    
    // Draw phase output lines
    drawPhaseLine(canvas, t1X1, phaseAEnd, 'a');
    drawPhaseLine(canvas, t2X1, phaseBEnd, 'b');
    drawPhaseLine(canvas, t3X1, phaseCEnd, 'c');
    
    // Draw secondary neutral point (Wye center)
    final secondaryNeutral = Offset(centerX + 60, centerY + 40);
    
    // X2 terminals on transformers
    final t1X2 = Offset(centerX + 40, centerY - 60);
    final t2X2 = Offset(centerX + 40, centerY + 20);
    final t3X2 = Offset(centerX + 40, centerY + 100);
    
    // Draw connections from X2 terminals to neutral point
    canvas.drawLine(t1X2, secondaryNeutral, linePaint);
    canvas.drawLine(t2X2, secondaryNeutral, linePaint);
    canvas.drawLine(t3X2, secondaryNeutral, linePaint);
    
    // Draw neutral output line
    canvas.drawLine(secondaryNeutral, neutralEnd, linePaint);
    
    // Draw neutral symbols
    drawNeutralSymbol(canvas, secondaryNeutral);
    drawNeutralSymbol(canvas, neutralEnd);
    
    // Draw ground connection for secondary neutral
    drawGroundSymbol(canvas, Offset(neutralEnd.dx, neutralEnd.dy + 30));
  }

  /// Draw voltage measurement labels
  void _drawVoltageLabels(Canvas canvas, double centerX, double centerY) {
    // Primary voltage labels
    _drawText(canvas, '7200V', Offset(centerX - 100, centerY - 100), voltageStyle);
    
    // Secondary voltage labels
    drawVoltageIndicator(
      canvas,
      Offset(centerX + 150, centerY - 80),
      Offset(centerX + 150, centerY + 120),
      '120V',
    );
    
    drawVoltageIndicator(
      canvas,
      Offset(centerX + 150, centerY - 80),
      Offset(centerX + 150, centerY),
      '240V',
    );
  }

  /// Draw connection terminals for interactive use
  void _drawConnectionTerminals(Canvas canvas, double centerX, double centerY) {
    // Primary input terminals
    drawTerminal(canvas, Offset(centerX - 150, centerY - 80), 'A', 
                 isInput: true, type: ConnectionType.primary);
    drawTerminal(canvas, Offset(centerX - 150, centerY), 'B', 
                 isInput: true, type: ConnectionType.primary);
    drawTerminal(canvas, Offset(centerX - 150, centerY + 80), 'C', 
                 isInput: true, type: ConnectionType.primary);
    
    // Transformer primary terminals
    drawTerminal(canvas, Offset(centerX - 40, centerY - 80), 'H1');
    drawTerminal(canvas, Offset(centerX - 40, centerY - 60), 'H2');
    drawTerminal(canvas, Offset(centerX - 40, centerY), 'H1');
    drawTerminal(canvas, Offset(centerX - 40, centerY + 20), 'H2');
    drawTerminal(canvas, Offset(centerX - 40, centerY + 80), 'H1');
    drawTerminal(canvas, Offset(centerX - 40, centerY + 100), 'H2');
    
    // Transformer secondary terminals
    drawTerminal(canvas, Offset(centerX + 40, centerY - 80), 'X1', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 40, centerY - 60), 'X2', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 40, centerY), 'X1', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 40, centerY + 20), 'X2', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 40, centerY + 80), 'X1', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 40, centerY + 100), 'X2', 
                 type: ConnectionType.secondary);
    
    // Output terminals
    drawTerminal(canvas, Offset(centerX + 150, centerY - 80), 'a', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 150, centerY), 'b', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 150, centerY + 80), 'c', 
                 type: ConnectionType.secondary);
    drawTerminal(canvas, Offset(centerX + 150, centerY + 120), 'N', 
                 type: ConnectionType.neutral);
  }

  /// Helper method to draw text (override from base class to access here)
  void _drawText(Canvas canvas, String text, Offset position, TextStyle style) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, position);
  }
}
