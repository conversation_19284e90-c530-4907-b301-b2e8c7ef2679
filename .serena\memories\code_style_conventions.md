# Code Style and Conventions

## Dart/Flutter Standards
- Flutter 3.x with null safety
- Follows `package:flutter_lints/flutter.yaml` rules
- Uses analysis_options.yaml for static analysis
- Prefer relative imports within features, absolute for cross-feature

## Naming Conventions
- **Components**: Use `JJ` prefix for custom components (e.g., `JJButton`, `JJElectricalLoader`)
- **Files**: snake_case for file names
- **Classes**: PascalCase
- **Variables**: camelCase
- **Constants**: UPPER_SNAKE_CASE

## Project Structure
```
lib/
├── screens/         # Screen widgets (feature-based)
├── widgets/         # Reusable components
├── services/        # Business logic and API calls
├── providers/       # State management (Provider pattern)
├── models/          # Data models
├── design_system/   # Theme and design components
├── electrical_components/ # Electrical-themed UI components
└── navigation/      # Router configuration
```

## Design System
- **Primary Colors**: Navy (#1A202C) and Copper (#B45309)
- **Typography**: Google Fonts Inter
- **Theme**: Use `AppTheme` constants from `lib/design_system/app_theme.dart`
- **Electrical Elements**: Circuit patterns, lightning bolts, electrical symbols

## Documentation
- Use comprehensive dartdoc comments
- Document widget purposes and parameters
- Include usage examples for complex components
- Document Firebase collections and schemas