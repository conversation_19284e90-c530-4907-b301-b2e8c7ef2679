import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '/models/locals_record.dart';
import '/design_system/app_theme.dart';
import '/design_system/popup_theme.dart';
import '/providers/app_state_provider.dart';
import 'dart:io' show Platform;
import '../../widgets/notification_badge.dart';
import 'package:go_router/go_router.dart';
import '../../navigation/app_router.dart';

class LocalsScreen extends StatefulWidget {
  const LocalsScreen({super.key});

  @override
  State<LocalsScreen> createState() => _LocalsScreenState();
}

class _LocalsScreenState extends State<LocalsScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  String _searchQuery = '';
  String? _selectedState;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppStateProvider>().refreshLocals();
    });
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final appStateProvider = context.read<AppStateProvider>();
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent * 0.8 && 
        !appStateProvider.isLoadingLocals && 
        appStateProvider.hasMoreLocals) {
      appStateProvider.loadMoreLocals();
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.offWhite,
      appBar: AppBar(
        title: const Text('IBEW Locals Directory'),
        backgroundColor: AppTheme.primaryNavy,
        elevation: 0,
        actions: [
          NotificationBadge(
            iconColor: AppTheme.white,
            showPopupOnTap: false,
            onTap: () {
              context.push(AppRouter.notifications);
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            child: TextField(
              controller: _searchController,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.white),
              decoration: InputDecoration(
                hintText: 'Search by local number, city, or state...',
                hintStyle: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.white.withAlpha(179)
                ),
                prefixIcon: const Icon(Icons.search, color: AppTheme.white),
                filled: true,
                fillColor: AppTheme.white.withAlpha(26),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  borderSide: const BorderSide(
                    color: AppTheme.accentCopper, 
                    width: 2
                  ),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value.toLowerCase();
                });
                // Handle search on the UI side for now
              },
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          // State filter dropdown
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMd),
            child: DropdownButton<String>(
              value: _selectedState,
              hint: const Text('Filter by State'),
              isExpanded: true,
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('All States'),
                ),
                // Common states - you can expand this list
                ...['CA', 'TX', 'NY', 'FL', 'PA', 'IL', 'OH', 'GA', 'NC', 'MI']
                    .map((state) => DropdownMenuItem<String>(
                          value: state,
                          child: Text(state),
                        )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedState = value;
                });
                context.read<AppStateProvider>().refreshLocals();
              },
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          // Locals list
          Expanded(
            child: Consumer<AppStateProvider>(
              builder: (context, appStateProvider, child) {
                return _buildLocalsList(appStateProvider);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocalsList(AppStateProvider appStateProvider) {
    if (appStateProvider.isLoadingLocals && appStateProvider.locals.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentCopper),
        ),
      );
    }

    if (appStateProvider.localsError != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: AppTheme.iconXxl,
              color: AppTheme.errorRed,
            ),
            const SizedBox(height: AppTheme.spacingMd),
            Text(
              'Error loading locals',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.errorRed
              ),
            ),
            const SizedBox(height: AppTheme.spacingSm),
            Text(
              appStateProvider.localsError!,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingMd),
            ElevatedButton(
              onPressed: () => appStateProvider.refreshLocals(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // Filter locals based on search query
    List<LocalsRecord> filteredLocals = appStateProvider.locals;
    if (_searchQuery.isNotEmpty) {
      filteredLocals = appStateProvider.locals.where((local) {
        return local.localUnion.toLowerCase().contains(_searchQuery) ||
               local.city.toLowerCase().contains(_searchQuery) ||
               local.state.toLowerCase().contains(_searchQuery) ||
               (local.classification?.toLowerCase().contains(_searchQuery) ?? false);
      }).toList();
    }
    
    // Sort locals numerically by local union number (convert to int for proper sorting)
    filteredLocals.sort((a, b) {
      final aNum = int.tryParse(a.localUnion) ?? 0;
      final bNum = int.tryParse(b.localUnion) ?? 0;
      return aNum.compareTo(bNum);
    });

    if (filteredLocals.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_city,
              size: AppTheme.iconXxl,
              color: AppTheme.mediumGray,
            ),
            const SizedBox(height: AppTheme.spacingMd),
            Text(
              _searchQuery.isEmpty 
                  ? 'No locals found' 
                  : 'No results found',
              style: AppTheme.headlineSmall.copyWith(
                color: AppTheme.textLight
              ),
            ),
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: AppTheme.spacingSm),
              Text(
                'Try adjusting your search',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textLight
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      itemCount: filteredLocals.length + (appStateProvider.hasMoreLocals ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == filteredLocals.length) {
          // Loading indicator at the bottom
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              child: appStateProvider.isLoadingLocals
                  ? const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppTheme.accentCopper),
                    )
                  : ElevatedButton(
                      onPressed: () => appStateProvider.loadMoreLocals(),
                      child: const Text('Load More'),
                    ),
            ),
          );
        }

        final local = filteredLocals[index];
        return LocalCard(
          local: local,
          onTap: () => _showLocalDetails(context, local),
        );
      },
    );
  }

  void _showLocalDetails(BuildContext context, LocalsRecord local) {
    showDialog(
      context: context,
      barrierColor: PopupThemeData.wide().barrierColor ?? Colors.black54,
      builder: (context) => PopupTheme(
        data: PopupThemeData.wide(),
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: const EdgeInsets.all(AppTheme.spacingLg),
          child: LocalDetailsDialog(local: local),
        ),
      ),
    );
  }
}

class LocalCard extends StatelessWidget {
  final LocalsRecord local;
  final VoidCallback onTap;

  const LocalCard({
    super.key,
    required this.local,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        side: const BorderSide(
          color: AppTheme.accentCopper,
          width: AppTheme.borderWidthThin,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingLg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Local ${local.localUnion}',
                          style: AppTheme.headlineSmall.copyWith(
                            color: AppTheme.primaryNavy,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingXs),
                        Text(
                          '${local.city}, ${local.state}',
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppTheme.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacingSm,
                      vertical: AppTheme.spacingXs,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.accentCopper.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                    ),
                    child: Icon(
                      Icons.arrow_forward_ios,
                      size: AppTheme.iconSm,
                      color: AppTheme.accentCopper,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingMd),
              // Content
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (local.address?.isNotEmpty == true) ...[
                    _buildInfoRow(
                      context,
                      'Address',
                      local.address!,
                      Icons.location_on_outlined,
                      canTap: true,
                      onTap: () => _launchMaps(local.address!, local.city, local.state),
                    ),
                    const SizedBox(height: AppTheme.spacingSm),
                  ],
                  if (local.phone.isNotEmpty) ...[
                    _buildInfoRow(
                      context,
                      'Phone',
                      local.phone,
                      Icons.phone_outlined,
                      canTap: true,
                      onTap: () => _launchPhone(local.phone),
                    ),
                    const SizedBox(height: AppTheme.spacingSm),
                  ],
                  if (local.email.isNotEmpty) ...[
                    _buildInfoRow(
                      context,
                      'Email',
                      local.email,
                      Icons.email_outlined,
                      canTap: true,
                      onTap: () => _launchEmail(local.email),
                    ),
                    const SizedBox(height: AppTheme.spacingSm),
                  ],
                  if (local.website?.isNotEmpty == true) ...[
                    _buildInfoRow(
                      context,
                      'Website',
                      local.website!,
                      Icons.language_outlined,
                      canTap: true,
                      onTap: () => _launchWebsite(local.website!),
                    ),
                    const SizedBox(height: AppTheme.spacingSm),
                  ],
                  _buildInfoRow(
                    context,
                    'Classification',
                    local.classification ?? 'Unknown',
                    Icons.work_outline,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    bool canTap = false,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: canTap ? onTap : null,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: AppTheme.iconMd,
            color: AppTheme.textLight,
          ),
          const SizedBox(width: AppTheme.spacingSm),
          Expanded(
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '$label: ',
                    style: AppTheme.labelMedium.copyWith(
                      color: AppTheme.textLight,
                    ),
                  ),
                  TextSpan(
                    text: value,
                    style: AppTheme.bodyMedium.copyWith(
                      color: canTap ? AppTheme.accentCopper : AppTheme.textDark,
                      decoration: canTap ? TextDecoration.underline : null,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchPhone(String phone) async {
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d+]'), '');
    final Uri phoneUri = Uri(scheme: 'tel', path: cleanPhone);
    
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(scheme: 'mailto', path: email);
    
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  Future<void> _launchWebsite(String website) async {
    String url = website;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }
    
    final Uri webUri = Uri.parse(url);
    
    if (await canLaunchUrl(webUri)) {
      await launchUrl(webUri, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _launchMaps(String address, String city, String state) async {
    final fullAddress = '$address, $city, $state';
    final encodedAddress = Uri.encodeComponent(fullAddress);
    
    Uri mapsUri;
    if (Platform.isIOS) {
      mapsUri = Uri.parse('maps://?q=$encodedAddress');
      if (!await canLaunchUrl(mapsUri)) {
        mapsUri = Uri.parse('https://maps.apple.com/?q=$encodedAddress');
      }
    } else {
      mapsUri = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=$encodedAddress'
      );
    }
    
    if (await canLaunchUrl(mapsUri)) {
      await launchUrl(mapsUri, mode: LaunchMode.externalApplication);
    }
  }
}

class LocalDetailsDialog extends StatelessWidget {
  final LocalsRecord local;

  const LocalDetailsDialog({super.key, required this.local});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.95,
        ),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(AppTheme.radiusXl),
          border: Border.all(
            color: AppTheme.accentCopper,
            width: AppTheme.borderWidthThick,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryNavy.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: AppTheme.accentCopper.withValues(alpha: 0.1),
              blurRadius: 10,
              spreadRadius: 2,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with gradient background
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppTheme.spacingLg),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryNavy,
                    AppTheme.secondaryNavy,
                  ],
                  stops: [0.0, 1.0],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.radiusXl),
                  topRight: Radius.circular(AppTheme.radiusXl),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppTheme.spacingSm,
                                vertical: AppTheme.spacingXs,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.accentCopper,
                                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                              ),
                              child: Icon(
                                Icons.flash_on,
                                size: AppTheme.iconSm,
                                color: AppTheme.white,
                              ),
                            ),
                            const SizedBox(width: AppTheme.spacingSm),
                            Expanded(
                              child: Text(
                                'IBEW Local ${local.localUnion}',
                                style: AppTheme.headlineMedium.copyWith(
                                  color: AppTheme.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: AppTheme.spacingXs),
                        Text(
                          '${local.city}, ${local.state}',
                          style: AppTheme.bodyLarge.copyWith(
                            color: AppTheme.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: AppTheme.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(AppTheme.radiusRound),
                    ),
                    child: IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      color: AppTheme.white,
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppTheme.spacingLg),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Quick Actions
                    if (local.phone.isNotEmpty || local.email.isNotEmpty || local.website?.isNotEmpty == true) 
                      _buildQuickActions(local),
                    const SizedBox(height: AppTheme.spacingLg),
                    // Details Section
                    _buildDetailsSection(local),
                    // Additional Information
                    if (local.data?.isNotEmpty == true) 
                      ..._buildAdditionalInfo(local),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(LocalsRecord local) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTheme.headlineSmall.copyWith(
            color: AppTheme.primaryNavy,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),
        Row(
          children: [
            if (local.phone.isNotEmpty) 
              Expanded(
                child: _buildActionButton(
                  icon: Icons.phone,
                  label: 'Call',
                  onTap: () => _launchPhone(local.phone),
                ),
              ),
            if (local.phone.isNotEmpty && local.email.isNotEmpty)
              const SizedBox(width: AppTheme.spacingSm),
            if (local.email.isNotEmpty) 
              Expanded(
                child: _buildActionButton(
                  icon: Icons.email,
                  label: 'Email',
                  onTap: () => _launchEmail(local.email),
                ),
              ),
            if ((local.phone.isNotEmpty || local.email.isNotEmpty) && local.website?.isNotEmpty == true)
              const SizedBox(width: AppTheme.spacingSm),
            if (local.website?.isNotEmpty == true) 
              Expanded(
                child: _buildActionButton(
                  icon: Icons.language,
                  label: 'Website',
                  onTap: () => _launchWebsite(local.website!),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.radiusMd),
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: AppTheme.spacingMd,
          horizontal: AppTheme.spacingSm,
        ),
        decoration: BoxDecoration(
          color: AppTheme.accentCopper.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusMd),
          border: Border.all(
            color: AppTheme.accentCopper.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: AppTheme.accentCopper,
              size: AppTheme.iconMd,
            ),
            const SizedBox(height: AppTheme.spacingXs),
            Text(
              label,
              style: AppTheme.labelSmall.copyWith(
                color: AppTheme.accentCopper,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection(LocalsRecord local) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Local Information',
          style: AppTheme.headlineSmall.copyWith(
            color: AppTheme.primaryNavy,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingMd),
          decoration: BoxDecoration(
            color: AppTheme.primaryNavy.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(AppTheme.radiusMd),
            border: Border.all(
              color: AppTheme.primaryNavy.withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: [
              _buildDetailRow(
                'Classification',
                local.classification ?? 'Unknown',
                Icons.work_outline,
              ),
              if (local.address?.isNotEmpty == true)
                _buildDetailRow(
                  'Address',
                  local.address!,
                  Icons.location_on_outlined,
                  canTap: true,
                  onTap: () => _launchMaps(local.address!, local.city, local.state),
                ),
              _buildDetailRow(
                'Phone',
                local.phone.isNotEmpty ? local.phone : 'Not specified',
                Icons.phone_outlined,
                canTap: local.phone.isNotEmpty,
                onTap: local.phone.isNotEmpty ? () => _launchPhone(local.phone) : null,
              ),
              _buildDetailRow(
                'Email',
                local.email.isNotEmpty ? local.email : 'Not specified',
                Icons.email_outlined,
                canTap: local.email.isNotEmpty,
                onTap: local.email.isNotEmpty ? () => _launchEmail(local.email) : null,
              ),
              if (local.website?.isNotEmpty == true)
                _buildDetailRow(
                  'Website',
                  local.website!,
                  Icons.language_outlined,
                  canTap: true,
                  onTap: () => _launchWebsite(local.website!),
                ),
            ],
          ),
        ),
      ],
    );
  }

  List<Widget> _buildAdditionalInfo(LocalsRecord local) {
    return [
      const SizedBox(height: AppTheme.spacingLg),
      Text(
        'Additional Information',
        style: AppTheme.headlineSmall.copyWith(
          color: AppTheme.primaryNavy,
          fontWeight: FontWeight.bold,
        ),
      ),
      const SizedBox(height: AppTheme.spacingMd),
      Container(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: AppTheme.accentCopper.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(AppTheme.radiusMd),
          border: Border.all(
            color: AppTheme.accentCopper.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: local.data!.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.symmetric(
                vertical: AppTheme.spacingXs,
              ),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '${entry.key}: ',
                      style: AppTheme.labelMedium.copyWith(
                        color: AppTheme.accentCopper,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: entry.value.toString(),
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.textDark,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    ];
  }

  Widget _buildDetailRow(
    String label,
    String value, 
    IconData icon, {
    bool canTap = false,
    VoidCallback? onTap,
  }) {
    if (value.isEmpty) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingSm),
      child: InkWell(
        onTap: canTap ? onTap : null,
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingXs),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingXs),
                decoration: BoxDecoration(
                  color: canTap 
                      ? AppTheme.accentCopper.withValues(alpha: 0.1)
                      : AppTheme.primaryNavy.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                ),
                child: Icon(
                  icon,
                  size: AppTheme.iconSm,
                  color: canTap ? AppTheme.accentCopper : AppTheme.primaryNavy,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: AppTheme.labelMedium.copyWith(
                        color: AppTheme.textLight,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingXs),
                    Text(
                      value,
                      style: AppTheme.bodyMedium.copyWith(
                        color: canTap ? AppTheme.accentCopper : AppTheme.textDark,
                        decoration: canTap ? TextDecoration.underline : null,
                        fontWeight: canTap ? FontWeight.w500 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
              if (canTap)
                Icon(
                  Icons.open_in_new,
                  size: AppTheme.iconSm,
                  color: AppTheme.accentCopper.withValues(alpha: 0.7),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _launchPhone(String phone) async {
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d+]'), '');
    final Uri phoneUri = Uri(scheme: 'tel', path: cleanPhone);
    
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(scheme: 'mailto', path: email);
    
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  Future<void> _launchWebsite(String website) async {
    String url = website;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }
    
    final Uri webUri = Uri.parse(url);
    
    if (await canLaunchUrl(webUri)) {
      await launchUrl(webUri, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _launchMaps(String address, String city, String state) async {
    final fullAddress = '$address, $city, $state';
    final encodedAddress = Uri.encodeComponent(fullAddress);
    
    Uri mapsUri;
    if (Platform.isIOS) {
      mapsUri = Uri.parse('maps://?q=$encodedAddress');
      if (!await canLaunchUrl(mapsUri)) {
        mapsUri = Uri.parse('https://maps.apple.com/?q=$encodedAddress');
      }
    } else {
      mapsUri = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=$encodedAddress'
      );
    }
    
    if (await canLaunchUrl(mapsUri)) {
      await launchUrl(mapsUri, mode: LaunchMode.externalApplication);
    }
  }
}