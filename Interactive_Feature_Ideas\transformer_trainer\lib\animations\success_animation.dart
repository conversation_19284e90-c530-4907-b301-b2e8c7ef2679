
import 'package:flutter/material.dart';

/// Success animation widget for correct connections
class SuccessAnimationWidget extends StatelessWidget {
  final AnimationController controller;
  final Widget child;

  const SuccessAnimationWidget({
    Key? key,
    required this.controller,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Transform.scale(
          scale: 1.0 + (0.1 * controller.value),
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.4 * controller.value),
                  spreadRadius: 8 * controller.value,
                  blurRadius: 12 * controller.value,
                ),
              ],
            ),
            child: child,
          ),
        );
      },
    );
  }
}

/// Pulse animation for highlighting correct connections
class PulseAnimationWidget extends StatelessWidget {
  final AnimationController controller;
  final Widget child;
  final Color color;

  const PulseAnimationWidget({
    Key? key,
    required this.controller,
    required this.child,
    this.color = Colors.green,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.6),
                spreadRadius: 10 * controller.value,
                blurRadius: 20 * controller.value,
              ),
            ],
          ),
          child: child,
        );
      },
    );
  }
}
