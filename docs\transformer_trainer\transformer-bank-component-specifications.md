# Transformer Bank Feature - Component Specifications

## 1. Core UI Components

### 1.1 JJModeSelector

**Purpose**: Toggle between Reference and Training modes
**Location**: `lib/design_system/components/jj_mode_selector.dart`

```dart
class JJModeSelector extends StatelessWidget {
  final String selectedMode;
  final List<ModeOption> options;
  final Function(String) onModeChanged;
  final bool isFullWidth;

  const JJModeSelector({
    Key? key,
    required this.selectedMode,
    required this.options,
    required this.onModeChanged,
    this.isFullWidth = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: isFullWidth ? double.infinity : null,
      decoration: BoxDecoration(
        color: AppTheme.lightGray,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        border: Border.all(
          color: AppTheme.accentCopper.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: options.map((option) => _buildModeOption(option)).toList(),
      ),
    );
  }

  Widget _buildModeOption(ModeOption option) {
    final isSelected = selectedMode == option.value;
    return Expanded(
      child: GestureDetector(
        onTap: () => onModeChanged(option.value),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          margin: const EdgeInsets.all(4),
          padding: const EdgeInsets.symmetric(
            vertical: AppTheme.spacingMd,
            horizontal: AppTheme.spacingLg,
          ),
          decoration: BoxDecoration(
            color: isSelected ? AppTheme.primaryNavy : Colors.transparent,
            borderRadius: BorderRadius.circular(AppTheme.radiusSm),
            boxShadow: isSelected ? [AppTheme.shadowSm] : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                option.icon,
                size: AppTheme.iconSm,
                color: isSelected ? AppTheme.white : AppTheme.textSecondary,
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Text(
                option.label,
                style: AppTheme.labelMedium.copyWith(
                  color: isSelected ? AppTheme.white : AppTheme.textSecondary,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ModeOption {
  final String value;
  final String label;
  final IconData icon;
  
  const ModeOption({
    required this.value,
    required this.label,
    required this.icon,
  });
}
```

### 1.2 JJDifficultyIndicator

**Purpose**: Visual indicator for current difficulty level
**Location**: `lib/design_system/components/jj_difficulty_indicator.dart`

```dart
class JJDifficultyIndicator extends StatelessWidget {
  final DifficultyLevel level;
  final bool showAnimation;
  final bool compact;

  const JJDifficultyIndicator({
    Key? key,
    required this.level,
    this.showAnimation = false,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(compact ? AppTheme.spacingSm : AppTheme.spacingMd),
      decoration: BoxDecoration(
        gradient: _getDifficultyGradient(level),
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        boxShadow: [AppTheme.shadowSm],
      ),
      child: compact ? _buildCompactLayout() : _buildFullLayout(),
    );
  }

  Widget _buildFullLayout() {
    return Row(
      children: [
        _buildDifficultyIcon(),
        const SizedBox(width: AppTheme.spacingMd),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getDifficultyTitle(level),
                style: AppTheme.titleMedium.copyWith(
                  color: AppTheme.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppTheme.spacingXs),
              Text(
                _getDifficultySubtitle(level),
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
        ),
        _buildDifficultyStars(),
      ],
    );
  }

  Widget _buildCompactLayout() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildDifficultyIcon(),
        const SizedBox(width: AppTheme.spacingSm),
        Text(
          _getDifficultyTitle(level),
          style: AppTheme.labelMedium.copyWith(
            color: AppTheme.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: AppTheme.spacingSm),
        _buildDifficultyStars(),
      ],
    );
  }

  Widget _buildDifficultyIcon() {
    IconData icon;
    switch (level) {
      case DifficultyLevel.beginner:
        icon = Icons.school;
        break;
      case DifficultyLevel.intermediate:
        icon = Icons.work;
        break;
      case DifficultyLevel.advanced:
        icon = Icons.engineering;
        break;
    }

    return showAnimation
        ? _AnimatedDifficultyIcon(icon: icon, level: level)
        : Icon(icon, color: AppTheme.white, size: AppTheme.iconMd);
  }

  Widget _buildDifficultyStars() {
    int starCount = level.index + 1;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return Icon(
          index < starCount ? Icons.star : Icons.star_border,
          color: AppTheme.white,
          size: compact ? AppTheme.iconSm : AppTheme.iconMd,
        );
      }),
    );
  }

  LinearGradient _getDifficultyGradient(DifficultyLevel level) {
    switch (level) {
      case DifficultyLevel.beginner:
        return const LinearGradient(
          colors: [Color(0xFF34D399), Color(0xFF059669)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case DifficultyLevel.intermediate:
        return const LinearGradient(
          colors: [Color(0xFFF59E0B), Color(0xFFD97706)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case DifficultyLevel.advanced:
        return const LinearGradient(
          colors: [Color(0xFFDC2626), Color(0xFF991B1B)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }
}

class _AnimatedDifficultyIcon extends StatefulWidget {
  final IconData icon;
  final DifficultyLevel level;

  const _AnimatedDifficultyIcon({
    required this.icon,
    required this.level,
  });

  @override
  State<_AnimatedDifficultyIcon> createState() => _AnimatedDifficultyIconState();
}

class _AnimatedDifficultyIconState extends State<_AnimatedDifficultyIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(
        milliseconds: widget.level == DifficultyLevel.beginner
            ? 1500
            : widget.level == DifficultyLevel.intermediate
                ? 1000
                : 500,
      ),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: _animation.value,
          child: Icon(
            widget.icon,
            color: AppTheme.white,
            size: AppTheme.iconMd,
          ),
        );
      },
    );
  }
}
```

### 1.3 JJConfigurationCard

**Purpose**: Display transformer configuration options with visual diagrams
**Location**: `lib/design_system/components/jj_configuration_card.dart`

```dart
class JJConfigurationCard extends StatelessWidget {
  final TransformerBankType bankType;
  final String title;
  final String subtitle;
  final String description;
  final VoidCallback onTap;
  final bool isSelected;
  final bool showDiagram;

  const JJConfigurationCard({
    Key? key,
    required this.bankType,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.onTap,
    this.isSelected = false,
    this.showDiagram = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(AppTheme.radiusLg),
          border: Border.all(
            color: isSelected ? AppTheme.accentCopper : AppTheme.borderLight,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [AppTheme.shadowMd]
              : [AppTheme.shadowSm],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTheme.titleLarge.copyWith(
                          color: AppTheme.primaryNavy,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingXs),
                      Text(
                        subtitle,
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppTheme.accentCopper,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: const EdgeInsets.all(AppTheme.spacingSm),
                    decoration: BoxDecoration(
                      color: AppTheme.accentCopper,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      color: AppTheme.white,
                      size: AppTheme.iconSm,
                    ),
                  ),
              ],
            ),
            if (showDiagram) ...[
              const SizedBox(height: AppTheme.spacingMd),
              _buildDiagramPreview(),
            ],
            const SizedBox(height: AppTheme.spacingMd),
            Text(
              description,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: AppTheme.spacingMd),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getBankTypeText(bankType),
                  style: AppTheme.labelSmall.copyWith(
                    color: AppTheme.textLight,
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: AppTheme.iconSm,
                  color: isSelected ? AppTheme.accentCopper : AppTheme.textLight,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiagramPreview() {
    return Container(
      height: 80,
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppTheme.lightGray,
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
      ),
      child: CustomPaint(
        painter: _ConfigurationDiagramPainter(bankType),
      ),
    );
  }

  String _getBankTypeText(TransformerBankType type) {
    switch (type) {
      case TransformerBankType.wyeToWye:
        return '3-POT BANK';
      case TransformerBankType.deltaToDelta:
        return '3-POT BANK';
      case TransformerBankType.wyeToDelta:
        return '3-POT BANK';
      case TransformerBankType.deltaToWye:
        return '3-POT BANK';
      case TransformerBankType.openDelta:
        return '2-POT BANK';
    }
  }
}

class _ConfigurationDiagramPainter extends CustomPainter {
  final TransformerBankType bankType;

  _ConfigurationDiagramPainter(this.bankType);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.primaryNavy
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.height * 0.3;

    switch (bankType) {
      case TransformerBankType.wyeToWye:
        _drawWyeConfiguration(canvas, center, radius, paint);
        break;
      case TransformerBankType.deltaToDelta:
        _drawDeltaConfiguration(canvas, center, radius, paint);
        break;
      case TransformerBankType.wyeToDelta:
        _drawWyeDeltaConfiguration(canvas, center, radius, paint);
        break;
      case TransformerBankType.deltaToWye:
        _drawDeltaWyeConfiguration(canvas, center, radius, paint);
        break;
      case TransformerBankType.openDelta:
        _drawOpenDeltaConfiguration(canvas, center, radius, paint);
        break;
    }
  }

  void _drawWyeConfiguration(Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw three lines meeting at center (Y shape)
    final angles = [0, 2.09, 4.19]; // 120 degrees apart
    for (final angle in angles) {
      final start = center;
      final end = Offset(
        center.dx + radius * cos(angle),
        center.dy + radius * sin(angle),
      );
      canvas.drawLine(start, end, paint);
    }
  }

  void _drawDeltaConfiguration(Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw triangle
    final path = Path();
    final angles = [1.57, 3.67, 5.76]; // Triangle points
    
    path.moveTo(
      center.dx + radius * cos(angles[0]),
      center.dy + radius * sin(angles[0]),
    );
    
    for (int i = 1; i < angles.length; i++) {
      path.lineTo(
        center.dx + radius * cos(angles[i]),
        center.dy + radius * sin(angles[i]),
      );
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  void _drawWyeDeltaConfiguration(Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw Y on left, triangle on right
    final leftCenter = Offset(center.dx - radius * 0.5, center.dy);
    final rightCenter = Offset(center.dx + radius * 0.5, center.dy);
    
    _drawWyeConfiguration(canvas, leftCenter, radius * 0.6, paint);
    _drawDeltaConfiguration(canvas, rightCenter, radius * 0.6, paint);
  }

  void _drawDeltaWyeConfiguration(Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw triangle on left, Y on right
    final leftCenter = Offset(center.dx - radius * 0.5, center.dy);
    final rightCenter = Offset(center.dx + radius * 0.5, center.dy);
    
    _drawDeltaConfiguration(canvas, leftCenter, radius * 0.6, paint);
    _drawWyeConfiguration(canvas, rightCenter, radius * 0.6, paint);
  }

  void _drawOpenDeltaConfiguration(Canvas canvas, Offset center, double radius, Paint paint) {
    // Draw incomplete triangle (open delta)
    final path = Path();
    final angles = [1.57, 3.67]; // Only two sides
    
    path.moveTo(
      center.dx + radius * cos(angles[0]),
      center.dy + radius * sin(angles[0]),
    );
    
    path.lineTo(
      center.dx + radius * cos(angles[1]),
      center.dy + radius * sin(angles[1]),
    );
    
    // Draw the other two sides separately
    canvas.drawPath(path, paint);
    
    // Draw second side
    canvas.drawLine(
      Offset(center.dx + radius * cos(angles[1]), center.dy + radius * sin(angles[1])),
      Offset(center.dx + radius * cos(5.76), center.dy + radius * sin(5.76)),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
```

### 1.4 JJProgressTracker

**Purpose**: Track and display training progress
**Location**: `lib/design_system/components/jj_progress_tracker.dart`

```dart
class JJProgressTracker extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final double progressPercentage;
  final int starsEarned;
  final int maxStars;
  final DifficultyLevel difficulty;
  final bool showAnimation;

  const JJProgressTracker({
    Key? key,
    required this.currentStep,
    required this.totalSteps,
    required this.progressPercentage,
    this.starsEarned = 0,
    this.maxStars = 3,
    required this.difficulty,
    this.showAnimation = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        boxShadow: [AppTheme.shadowSm],
      ),
      child: Column(
        children: [
          _buildProgressHeader(),
          const SizedBox(height: AppTheme.spacingMd),
          _buildProgressBar(),
          if (starsEarned > 0) ...[
            const SizedBox(height: AppTheme.spacingMd),
            _buildStarsRow(),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        JJDifficultyIndicator(
          level: difficulty,
          compact: true,
          showAnimation: showAnimation,
        ),
        Text(
          'Progress: $currentStep/$totalSteps',
          style: AppTheme.labelMedium.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${progressPercentage.toInt()}%',
              style: AppTheme.titleMedium.copyWith(
                color: AppTheme.primaryNavy,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (progressPercentage >= 100)
              Icon(
                Icons.check_circle,
                color: AppTheme.successGreen,
                size: AppTheme.iconMd,
              ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingSm),
        showAnimation
            ? _AnimatedProgressBar(
                progress: progressPercentage / 100,
                difficulty: difficulty,
              )
            : _StaticProgressBar(
                progress: progressPercentage / 100,
                difficulty: difficulty,
              ),
      ],
    );
  }

  Widget _buildStarsRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(maxStars, (index) {
        final isEarned = index < starsEarned;
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingXs),
          child: showAnimation && isEarned
              ? _AnimatedStar(delay: Duration(milliseconds: index * 200))
              : Icon(
                  isEarned ? Icons.star : Icons.star_border,
                  color: isEarned ? Colors.amber : AppTheme.mediumGray,
                  size: AppTheme.iconMd,
                ),
        );
      }),
    );
  }
}

class _StaticProgressBar extends StatelessWidget {
  final double progress;
  final DifficultyLevel difficulty;

  const _StaticProgressBar({
    required this.progress,
    required this.difficulty,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 8,
      decoration: BoxDecoration(
        color: AppTheme.lightGray,
        borderRadius: BorderRadius.circular(AppTheme.radiusXs),
      ),
      child: FractionallySizedBox(
        widthFactor: progress,
        alignment: Alignment.centerLeft,
        child: Container(
          decoration: BoxDecoration(
            gradient: _getDifficultyGradient(difficulty),
            borderRadius: BorderRadius.circular(AppTheme.radiusXs),
          ),
        ),
      ),
    );
  }

  LinearGradient _getDifficultyGradient(DifficultyLevel level) {
    switch (level) {
      case DifficultyLevel.beginner:
        return const LinearGradient(
          colors: [Color(0xFF34D399), Color(0xFF059669)],
        );
      case DifficultyLevel.intermediate:
        return const LinearGradient(
          colors: [Color(0xFFF59E0B), Color(0xFFD97706)],
        );
      case DifficultyLevel.advanced:
        return const LinearGradient(
          colors: [Color(0xFFDC2626), Color(0xFF991B1B)],
        );
    }
  }
}

class _AnimatedProgressBar extends StatefulWidget {
  final double progress;
  final DifficultyLevel difficulty;

  const _AnimatedProgressBar({
    required this.progress,
    required this.difficulty,
  });

  @override
  State<_AnimatedProgressBar> createState() => _AnimatedProgressBarState();
}

class _AnimatedProgressBarState extends State<_AnimatedProgressBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: widget.progress).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return _StaticProgressBar(
          progress: _animation.value,
          difficulty: widget.difficulty,
        );
      },
    );
  }
}

class _AnimatedStar extends StatefulWidget {
  final Duration delay;

  const _AnimatedStar({required this.delay});

  @override
  State<_AnimatedStar> createState() => _AnimatedStarState();
}

class _AnimatedStarState extends State<_AnimatedStar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    
    _rotationAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    Future.delayed(widget.delay, () {
      if (mounted) _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value * 2 * pi,
            child: Icon(
              Icons.star,
              color: Colors.amber,
              size: AppTheme.iconMd,
            ),
          ),
        );
      },
    );
  }
}
```

### 1.5 JJInstructionPanel

**Purpose**: Display step-by-step instructions with electrical safety information
**Location**: `lib/design_system/components/jj_instruction_panel.dart`

```dart
class JJInstructionPanel extends StatelessWidget {
  final String title;
  final String instruction;
  final String? tip;
  final String? safetyNote;
  final int stepNumber;
  final int totalSteps;
  final IconData? icon;
  final bool isExpanded;
  final VoidCallback? onToggle;

  const JJInstructionPanel({
    Key? key,
    required this.title,
    required this.instruction,
    this.tip,
    this.safetyNote,
    required this.stepNumber,
    required this.totalSteps,
    this.icon,
    this.isExpanded = true,
    this.onToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppTheme.spacingSm),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        border: Border.all(
          color: AppTheme.accentCopper.withOpacity(0.3),
        ),
        boxShadow: [AppTheme.shadowSm],
      ),
      child: Column(
        children: [
          _buildHeader(),
          if (isExpanded) _buildContent(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return GestureDetector(
      onTap: onToggle,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppTheme.accentCopper,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  stepNumber.toString(),
                  style: AppTheme.labelMedium.copyWith(
                    color: AppTheme.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.titleMedium.copyWith(
                      color: AppTheme.primaryNavy,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingXs),
                  Text(
                    'Step $stepNumber of $totalSteps',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (icon != null)
              Icon(
                icon,
                color: AppTheme.accentCopper,
                size: AppTheme.iconMd,
              ),
            if (onToggle != null) ...[
              const SizedBox(width: AppTheme.spacingSm),
              Icon(
                isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                color: AppTheme.textSecondary,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.only(
        left: AppTheme.spacingMd,
        right: AppTheme.spacingMd,
        bottom: AppTheme.spacingMd,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(height: 1),
          const SizedBox(height: AppTheme.spacingMd),
          
          // Main instruction
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.lightGray,
              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
            ),
            child: Text(
              instruction,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textPrimary,
              ),
            ),
          ),
          
          // Tip section
          if (tip != null) ...[
            const SizedBox(height: AppTheme.spacingMd),
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              decoration: BoxDecoration(
                color: AppTheme.accentCopper.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                border: Border.all(
                  color: AppTheme.accentCopper.withOpacity(0.3),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: AppTheme.accentCopper,
                    size: AppTheme.iconSm,
                  ),
                  const SizedBox(width: AppTheme.spacingSm),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pro Tip',
                          style: AppTheme.labelSmall.copyWith(
                            color: AppTheme.accentCopper,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingXs),
                        Text(
                          tip!,
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          // Safety note
          if (safetyNote != null) ...[
            const SizedBox(height: AppTheme.spacingMd),
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              decoration: BoxDecoration(
                color: AppTheme.warningYellow.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                border: Border.all(
                  color: AppTheme.warningYellow.withOpacity(0.3),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.warning,
                    color: AppTheme.warningYellow,
                    size: AppTheme.iconSm,
                  ),
                  const SizedBox(width: AppTheme.spacingSm),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Safety First',
                          style: AppTheme.labelSmall.copyWith(
                            color: AppTheme.warningYellow,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingXs),
                        Text(
                          safetyNote!,
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
```

## 2. Animation Components

### 2.1 JJElectricalFireAnimation

**Purpose**: Display electrical fire effect for incorrect connections
**Location**: `lib/electrical_components/animations/jj_electrical_fire_animation.dart`

```dart
class JJElectricalFireAnimation extends StatefulWidget {
  final Offset position;
  final VoidCallback? onComplete;
  final Duration duration;

  const JJElectricalFireAnimation({
    Key? key,
    required this.position,
    this.onComplete,
    this.duration = const Duration(seconds: 3),
  }) : super(key: key);

  @override
  State<JJElectricalFireAnimation> createState() => _JJElectricalFireAnimationState();
}

class _JJElectricalFireAnimationState extends State<JJElectricalFireAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _shakeController;
  late Animation<double> _fireAnimation;
  late Animation<double> _shakeAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _fireAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );
    
    _fadeAnimation = Tween<double>(begin: 1, end: 0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.6, 1.0, curve: Curves.easeIn),
      ),
    );
    
    _shakeAnimation = Tween<double>(begin: -5, end: 5).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticIn),
    );

    _startAnimation();
  }

  void _startAnimation() async {
    // Start shake effect
    _shakeController.repeat(reverse: true);
    
    // Start main fire animation
    await _controller.forward();
    
    // Stop shake
    _shakeController.stop();
    
    // Complete callback
    widget.onComplete?.call();
  }

  @override
  void dispose() {
    _controller.dispose();
    _shakeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_controller, _shakeController]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: CustomPaint(
              painter: _ElectricalFirePainter(
                animation: _fireAnimation.value,
                position: widget.position,
              ),
              size: Size.infinite,
            ),
          ),
        );
      },
    );
  }
}

class _ElectricalFirePainter extends CustomPainter {
  final double animation;
  final Offset position;

  _ElectricalFirePainter({
    required this.animation,
    required this.position,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (animation <= 0) return;

    final paint = Paint()
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

    // Draw fire particles
    final random = Random(42); // Seeded for consistent animation
    for (int i = 0; i < 20; i++) {
      final particleAnimation = (animation - (i * 0.05)).clamp(0.0, 1.0);
      if (particleAnimation <= 0) continue;

      final particleSize = 3 + random.nextDouble() * 5;
      final particleOffset = Offset(
        position.dx + (random.nextDouble() - 0.5) * 30 * animation,
        position.dy - random.nextDouble() * 40 * particleAnimation,
      );

      // Color gradient from red to orange to yellow
      final colors = [
        Colors.red.withOpacity(particleAnimation),
        Colors.orange.withOpacity(particleAnimation * 0.8),
        Colors.yellow.withOpacity(particleAnimation * 0.6),
      ];
      
      paint.color = colors[i % colors.length];
      canvas.drawCircle(particleOffset, particleSize * particleAnimation, paint);
    }

    // Draw electrical arc effect
    _drawElectricalArc(canvas, paint);
  }

  void _drawElectricalArc(Canvas canvas, Paint paint) {
    paint
      ..color = Colors.white.withOpacity(animation * 0.8)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    path.moveTo(position.dx - 20, position.dy);
    
    // Create zigzag pattern for electrical arc
    for (int i = 0; i < 5; i++) {
      final x = position.dx - 20 + (i * 8);
      final y = position.dy + (i % 2 == 0 ? -10 : 10) * animation;
      path.lineTo(x, y);
    }
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
```

### 2.2 JJSuccessAnimation

**Purpose**: Display success feedback for correct connections
**Location**: `lib/electrical_components/animations/jj_success_animation.dart`

```dart
class JJSuccessAnimation extends StatefulWidget {
  final Offset position;
  final VoidCallback? onComplete;
  final Color color;
  final Duration duration;

  const JJSuccessAnimation({
    Key? key,
    required this.position,
    this.onComplete,
    this.color = const Color(0xFF34D399),
    this.duration = const Duration(seconds: 2),
  }) : super(key: key);

  @override
  State<JJSuccessAnimation> createState() => _JJSuccessAnimationState();
}

class _JJSuccessAnimationState extends State<JJSuccessAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;
  late Animation<double> _sparkleAnimation;
  late Animation<double> _checkAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.3, curve: Curves.easeOut),
      ),
    );
    
    _sparkleAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 0.8, curve: Curves.easeInOut),
      ),
    );
    
    _checkAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 1.0, curve: Curves.elasticOut),
      ),
    );

    _controller.forward().then((_) => widget.onComplete?.call());
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          painter: _SuccessPainter(
            pulseAnimation: _pulseAnimation.value,
            sparkleAnimation: _sparkleAnimation.value,
            checkAnimation: _checkAnimation.value,
            position: widget.position,
            color: widget.color,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

class _SuccessPainter extends CustomPainter {
  final double pulseAnimation;
  final double sparkleAnimation;
  final double checkAnimation;
  final Offset position;
  final Color color;

  _SuccessPainter({
    required this.pulseAnimation,
    required this.sparkleAnimation,
    required this.checkAnimation,
    required this.position,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw pulse effect
    if (pulseAnimation > 0) {
      _drawPulse(canvas);
    }

    // Draw sparkles
    if (sparkleAnimation > 0) {
      _drawSparkles(canvas);
    }

    // Draw check mark
    if (checkAnimation > 0) {
      _drawCheckMark(canvas);
    }
  }

  void _drawPulse(Canvas canvas) {
    final paint = Paint()
      ..color = color.withOpacity((1 - pulseAnimation) * 0.5)
      ..style = PaintingStyle.fill;

    final radius = 30 * pulseAnimation;
    canvas.drawCircle(position, radius, paint);
  }

  void _drawSparkles(Canvas canvas) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(sparkleAnimation)
      ..style = PaintingStyle.fill;

    final random = Random(42);
    for (int i = 0; i < 8; i++) {
      final angle = (i * pi / 4) + sparkleAnimation * pi * 2;
      final distance = 25 + random.nextDouble() * 15;
      final sparklePos = Offset(
        position.dx + cos(angle) * distance * sparkleAnimation,
        position.dy + sin(angle) * distance * sparkleAnimation,
      );

      _drawStar(canvas, sparklePos, 3 * sparkleAnimation, paint);
    }
  }

  void _drawStar(Canvas canvas, Offset center, double size, Paint paint) {
    final path = Path();
    final spikes = 4;
    final outerRadius = size;
    final innerRadius = size * 0.5;

    for (int i = 0; i < spikes * 2; i++) {
      final angle = i * pi / spikes;
      final radius = i % 2 == 0 ? outerRadius : innerRadius;
      final x = center.dx + cos(angle) * radius;
      final y = center.dy + sin(angle) * radius;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  void _drawCheckMark(Canvas canvas) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();
    final checkSize = 20;
    
    // Animate the check mark drawing
    final progress = checkAnimation;
    
    // First stroke (short line)
    final start1 = Offset(position.dx - checkSize * 0.3, position.dy);
    final mid = Offset(position.dx - checkSize * 0.1, position.dy + checkSize * 0.3);
    
    if (progress > 0) {
      final firstStrokeProgress = (progress * 2).clamp(0.0, 1.0);
      final currentMid = Offset.lerp(start1, mid, firstStrokeProgress)!;
      
      path.moveTo(start1.dx, start1.dy);
      path.lineTo(currentMid.dx, currentMid.dy);
    }
    
    // Second stroke (long line)
    if (progress > 0.5) {
      final end = Offset(position.dx + checkSize * 0.4, position.dy - checkSize * 0.2);
      final secondStrokeProgress = ((progress - 0.5) * 2).clamp(0.0, 1.0);
      final currentEnd = Offset.lerp(mid, end, secondStrokeProgress)!;
      
      path.lineTo(currentEnd.dx, currentEnd.dy);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
```

## 3. Interactive Diagram Components

### 3.1 JJConnectionPoint

**Purpose**: Interactive connection points for transformer diagrams
**Location**: `lib/electrical_components/components/jj_connection_point.dart`

```dart
class JJConnectionPoint extends StatefulWidget {
  final String id;
  final String label;
  final ConnectionType type;
  final Offset position;
  final bool isHighlighted;
  final bool isConnected;
  final bool isSelected;
  final VoidCallback? onTap;
  final Function(String)? onHover;

  const JJConnectionPoint({
    Key? key,
    required this.id,
    required this.label,
    required this.type,
    required this.position,
    this.isHighlighted = false,
    this.isConnected = false,
    this.isSelected = false,
    this.onTap,
    this.onHover,
  }) : super(key: key);

  @override
  State<JJConnectionPoint> createState() => _JJConnectionPointState();
}

class _JJConnectionPointState extends State<JJConnectionPoint>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    if (widget.isSelected) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(JJConnectionPoint oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected && !oldWidget.isSelected) {
      _controller.repeat(reverse: true);
    } else if (!widget.isSelected && oldWidget.isSelected) {
      _controller.stop();
      _controller.reset();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: widget.position.dx - _getPointSize() / 2,
      top: widget.position.dy - _getPointSize() / 2,
      child: MouseRegion(
        onEnter: (_) {
          setState(() => _isHovered = true);
          widget.onHover?.call(widget.id);
        },
        onExit: (_) => setState(() => _isHovered = false),
        child: GestureDetector(
          onTap: widget.onTap,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              final scale = widget.isSelected ? _pulseAnimation.value : 1.0;
              return Transform.scale(
                scale: scale,
                child: Container(
                  width: _getPointSize(),
                  height: _getPointSize(),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getPointColor(),
                    border: Border.all(
                      color: _getBorderColor(),
                      width: _getBorderWidth(),
                    ),
                    boxShadow: _isHovered || widget.isHighlighted
                        ? [
                            BoxShadow(
                              color: _getPointColor().withOpacity(0.5),
                              blurRadius: 8,
                              spreadRadius: 2,
                            )
                          ]
                        : null,
                  ),
                  child: _buildLabel(),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  double _getPointSize() {
    switch (widget.type) {
      case ConnectionType.primary:
        return 24;
      case ConnectionType.secondary:
        return 20;
      case ConnectionType.neutral:
        return 18;
      case ConnectionType.ground:
        return 16;
    }
  }

  Color _getPointColor() {
    if (widget.isConnected) {
      switch (widget.type) {
        case ConnectionType.primary:
          return AppTheme.errorRed;
        case ConnectionType.secondary:
          return AppTheme.accentCopper;
        case ConnectionType.neutral:
          return AppTheme.mediumGray;
        case ConnectionType.ground:
          return AppTheme.successGreen;
      }
    }
    return AppTheme.lightGray;
  }

  Color _getBorderColor() {
    if (widget.isSelected) return AppTheme.accentCopper;
    if (widget.isHighlighted) return AppTheme.primaryNavy;
    return AppTheme.mediumGray;
  }

  double _getBorderWidth() {
    if (widget.isSelected || widget.isHighlighted) return 3;
    return 2;
  }

  Widget? _buildLabel() {
    if (widget.label.isEmpty) return null;
    
    return Center(
      child: Text(
        widget.label,
        style: AppTheme.labelSmall.copyWith(
          color: widget.isConnected ? AppTheme.white : AppTheme.textPrimary,
          fontWeight: FontWeight.bold,
          fontSize: 8,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
```

## 4. Utility Components

### 4.1 JJHintDialog

**Purpose**: Contextual help and hints during training
**Location**: `lib/design_system/components/jj_hint_dialog.dart`

```dart
class JJHintDialog extends StatelessWidget {
  final String title;
  final String content;
  final String? imageAsset;
  final IconData? icon;
  final VoidCallback? onClose;
  final VoidCallback? onNextHint;
  final VoidCallback? onPreviousHint;
  final bool showNavigation;

  const JJHintDialog({
    Key? key,
    required this.title,
    required this.content,
    this.imageAsset,
    this.icon,
    this.onClose,
    this.onNextHint,
    this.onPreviousHint,
    this.showNavigation = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        padding: const EdgeInsets.all(AppTheme.spacingLg),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            const SizedBox(height: AppTheme.spacingMd),
            _buildContent(),
            const SizedBox(height: AppTheme.spacingLg),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        if (icon != null)
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingSm),
            decoration: BoxDecoration(
              color: AppTheme.accentCopper.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: AppTheme.accentCopper,
              size: AppTheme.iconMd,
            ),
          ),
        if (icon != null) const SizedBox(width: AppTheme.spacingMd),
        Expanded(
          child: Text(
            title,
            style: AppTheme.headlineSmall.copyWith(
              color: AppTheme.primaryNavy,
            ),
          ),
        ),
        IconButton(
          onPressed: onClose,
          icon: Icon(
            Icons.close,
            color: AppTheme.textSecondary,
            size: AppTheme.iconSm,
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (imageAsset != null) ...[
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              image: DecorationImage(
                image: AssetImage(imageAsset!),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
        ],
        Text(
          content,
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.textPrimary,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (showNavigation && onPreviousHint != null)
          TextButton.icon(
            onPressed: onPreviousHint,
            icon: Icon(Icons.chevron_left, size: AppTheme.iconSm),
            label: Text('Previous'),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.textSecondary,
            ),
          )
        else
          const SizedBox.shrink(),
        
        JJPrimaryButton(
          text: 'Got it!',
          onPressed: onClose ?? () => Navigator.of(context).pop(),
          icon: Icons.check,
        ),
        
        if (showNavigation && onNextHint != null)
          TextButton.icon(
            onPressed: onNextHint,
            icon: Icon(Icons.chevron_right, size: AppTheme.iconSm),
            label: Text('Next'),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.accentCopper,
            ),
          )
        else
          const SizedBox.shrink(),
      ],
    );
  }
}
```

### 4.2 JJTimerDisplay

**Purpose**: Timer display for advanced difficulty challenges
**Location**: `lib/design_system/components/jj_timer_display.dart`

```dart
class JJTimerDisplay extends StatefulWidget {
  final Duration totalTime;
  final Duration remainingTime;
  final bool isRunning;
  final VoidCallback? onTimeUp;
  final Color? warningColor;
  final Duration? warningThreshold;

  const JJTimerDisplay({
    Key? key,
    required this.totalTime,
    required this.remainingTime,
    this.isRunning = false,
    this.onTimeUp,
    this.warningColor = const Color(0xFFDC2626),
    this.warningThreshold = const Duration(minutes: 1),
  }) : super(key: key);

  @override
  State<JJTimerDisplay> createState() => _JJTimerDisplayState();
}

class _JJTimerDisplayState extends State<JJTimerDisplay>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void didUpdateWidget(JJTimerDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if we should show warning animation
    if (_shouldShowWarning() && !_pulseController.isAnimating) {
      _pulseController.repeat(reverse: true);
    } else if (!_shouldShowWarning() && _pulseController.isAnimating) {
      _pulseController.stop();
      _pulseController.reset();
    }
    
    // Check if time is up
    if (widget.remainingTime.inSeconds <= 0 && oldWidget.remainingTime.inSeconds > 0) {
      widget.onTimeUp?.call();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  bool _shouldShowWarning() {
    return widget.warningThreshold != null &&
           widget.remainingTime <= widget.warningThreshold! &&
           widget.remainingTime.inSeconds > 0;
  }

  @override
  Widget build(BuildContext context) {
    final progress = widget.totalTime.inSeconds > 0
        ? widget.remainingTime.inSeconds / widget.totalTime.inSeconds
        : 0.0;
    
    final isWarning = _shouldShowWarning();
    final displayColor = isWarning ? widget.warningColor! : AppTheme.accentCopper;

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isWarning ? _pulseAnimation.value : 1.0,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingMd,
              vertical: AppTheme.spacingSm,
            ),
            decoration: BoxDecoration(
              color: displayColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              border: Border.all(
                color: displayColor,
                width: 2,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.timer,
                  color: displayColor,
                  size: AppTheme.iconMd,
                ),
                const SizedBox(width: AppTheme.spacingSm),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _formatTime(widget.remainingTime),
                      style: AppTheme.titleLarge.copyWith(
                        color: displayColor,
                        fontWeight: FontWeight.bold,
                        fontFeatures: [FontFeature.tabularFigures()],
                      ),
                    ),
                    SizedBox(
                      width: 80,
                      height: 4,
                      child: LinearProgressIndicator(
                        value: progress,
                        backgroundColor: AppTheme.lightGray,
                        valueColor: AlwaysStoppedAnimation<Color>(displayColor),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatTime(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
```

This comprehensive component specification provides all the necessary UI components for implementing the Transformer Bank feature with consistent design, proper accessibility, and engaging interactions. Each component follows the app's electrical theme and integrates seamlessly with the existing design system.