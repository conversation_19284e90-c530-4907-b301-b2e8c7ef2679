This file is a merged representation of the entire codebase, combining all repository files into a single document.
Generated by Repomix on: 2025-08-16 03:59:09

# File Summary

## Purpose

This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format

The content is organized as follows:

1. This summary section
2. Repository information
3. Repository structure
4. Multiple file entries, each consisting of:
   a. A header with the file path (## File: path/to/file)
   b. The full contents of the file in a code block

## Usage Guidelines

- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes

- Some files may have been excluded based on .gitignore rules and Repomix's
  configuration.
- Binary files are not included in this packed representation. Please refer to
  the Repository Structure section for a complete list of file paths, including
  binary files.

## Additional Information

For more information about Repomix, visit: <https://github.com/andersonby/python-repomix>

# Repository Structure

``` md
Claude Planning Prompts (2).md
<PERSON> Planning Prompts (3).md
<PERSON> Planning Prompts (4).md
<PERSON> Planning Prompts (5).md
Claude Planning Prompts (1).md
Claude Planning Prompts.md
```

# Repository Files

## Claude Planning Prompts (2).md

```markdown
\<goal\>  
You are an industry-veteran SaaS product designer. You’ve built high-touch UIs for FANG-style companies.

Your goal is to take the context below, the guidelines, the practicalities, the style guide, and the user inspiration, and turn it into a Design Brief  
\</goal\>

\<inspirations\>  
The attached images serve as the user’s inspiration (if any). You don’t need to take it literally in any way, but let it serve as an understanding of what the user likes aesthetically   
\</inspirations\>

\<guidelines\>  
\<aesthetics\>

- Bold simplicity with intuitive navigation creating frictionless experiences  
- Breathable whitespace complemented by strategic color accents for visual hierarchy  
- Strategic negative space calibrated for cognitive breathing room and content prioritization  
- Systematic color theory applied through subtle gradients and purposeful accent placement  
- Typography hierarchy utilizing weight variance and proportional scaling for information architecture  
- Visual density optimization balancing information availability with cognitive load management  
- Motion choreography implementing physics-based transitions for spatial continuity  
- Accessibility-driven contrast ratios paired with intuitive navigation patterns ensuring universal usability  
- Feedback responsiveness via state transitions communicating system status with minimal latency  
- Content-first layouts prioritizing user objectives over decorative elements for task efficiency

\</aesthetics\>

\<practicalities\>  
**FILL THIS IN**  
\</practicalities\>  
\</guidelines\>

\<context\>  
**FILL THIS IN**

\<task\>  
Your goal here is to go feature-by-feature and think like a product designer. Here is a list of things you’d absolutely need to think about:

**User goals and tasks** \- Understanding what users need to accomplish and designing to make those primary tasks seamless and efficient  
**Information architecture** \- Organizing content and features in a logical hierarchy that matches users' mental models  
**Progressive disclosure** \- Revealing complexity gradually to avoid overwhelming users while still providing access to advanced features  
**Visual hierarchy** \- Using size, color, contrast, and positioning to guide attention to the most important elements first  
**Affordances and signifiers** \- Making interactive elements clearly identifiable through visual cues that indicate how they work  
**Consistency** \- Maintaining uniform patterns, components, and interactions across screens to reduce cognitive load  
**Accessibility** \- Ensuring the design works for users of all abilities (color contrast, screen readers, keyboard navigation)  
**Error prevention** \- Designing to help users avoid mistakes before they happen rather than just handling errors after they occur  
**Feedback** \- Providing clear signals when actions succeed or fail, and communicating system status at all times  
**Performance considerations** \- Accounting for loading times and designing appropriate loading states  
**Mobile vs. desktop considerations** \- Adapting layouts and interactions for different device capabilities and contexts  
**Responsive design** \- Ensuring the interface works well across various screen sizes and orientations  
**User testing feedback loops** \- Incorporating iterative testing to validate assumptions and improve the design  
**Platform conventions** \- Following established patterns from iOS/Android/Web to meet user expectations  
**Microcopy and content strategy** \- Crafting clear, concise text that guides users through the experience  
**Aesthetic appeal** \- Creating a visually pleasing design that aligns with brand identity while prioritizing usability  
**Animations \-** Crafting beautiful yet subtle animations and transitions that make the app feel professional

I need you to take EACH FEATURE below, and give me a cohesive “Functional Brief”. Here’s how I want it formatted. You repeat this for each feature:

\<format\>  
\#\# Feature Name  
\#\#\# Screen X  
\#\#\#\# Screen X State N  
\* description  
\* of  
\* UI & UX  
\* in detail  
\* including animations  
\* any anything else  
\#\#\#\# Screen X State N+1

Repeat for as many N+\# as needed based on the function of the state

\</format\>  
\</task\>

\<feature-list\>

\</feature-list\>  
\</context\>
```

## Claude Planning Prompts (3).md

```markdown
\<goal\>  
You are an industry-veteran SaaS product designer. You’ve built high-touch UIs for FANG-style companies.

Your goal is to take the context below, the guidelines, the practicalities, the style guide, and the user inspiration, and turn it into a Design Brief  
\</goal\>

\<inspirations\>  
The attached images serve as the user’s inspiration (if any). You don’t need to take it literally in any way, but let it serve as an understanding of what the user likes aesthetically   
\</inspirations\>

\<guidelines\>  
\<aesthetics\>

- Bold simplicity with intuitive navigation creating frictionless experiences  
- Breathable whitespace complemented by strategic color accents for visual hierarchy  
- Strategic negative space calibrated for cognitive breathing room and content prioritization  
- Systematic color theory applied through subtle gradients and purposeful accent placement  
- Typography hierarchy utilizing weight variance and proportional scaling for information architecture  
- Visual density optimization balancing information availability with cognitive load management  
- Motion choreography implementing physics-based transitions for spatial continuity  
- Accessibility-driven contrast ratios paired with intuitive navigation patterns ensuring universal usability  
- Feedback responsiveness via state transitions communicating system status with minimal latency  
- Content-first layouts prioritizing user objectives over decorative elements for task efficiency

\</aesthetics\>

\<practicalities\>

- This will be an iOS and Android app  
- I want a techy white and maroon-red deep color palette

\</practicalities\>  
\</guidelines\>

\<context\>  
\<app-overview\>  
**This is an app where you take pictures of dishes at a restaurant, and give the menu description and your tasting notes, and it produces you a michelin star version of that recipe to cook at home**  
\</app-overview\>  
\<task\>  
Your goal here is to go feature-by-feature and think like a product designer. Here is a list of things you’d absolutely need to think about:

**User goals and tasks** \- Understanding what users need to accomplish and designing to make those primary tasks seamless and efficient  
**Information architecture** \- Organizing content and features in a logical hierarchy that matches users' mental models  
**Progressive disclosure** \- Revealing complexity gradually to avoid overwhelming users while still providing access to advanced features  
**Visual hierarchy** \- Using size, color, contrast, and positioning to guide attention to the most important elements first  
**Affordances and signifiers** \- Making interactive elements clearly identifiable through visual cues that indicate how they work  
**Consistency** \- Maintaining uniform patterns, components, and interactions across screens to reduce cognitive load  
**Accessibility** \- Ensuring the design works for users of all abilities (color contrast, screen readers, keyboard navigation)  
**Error prevention** \- Designing to help users avoid mistakes before they happen rather than just handling errors after they occur  
**Feedback** \- Providing clear signals when actions succeed or fail, and communicating system status at all times  
**Performance considerations** \- Accounting for loading times and designing appropriate loading states  
**Mobile vs. desktop considerations** \- Adapting layouts and interactions for different device capabilities and contexts  
**Responsive design** \- Ensuring the interface works well across various screen sizes and orientations  
**User testing feedback loops** \- Incorporating iterative testing to validate assumptions and improve the design  
**Platform conventions** \- Following established patterns from iOS/Android/Web to meet user expectations  
**Microcopy and content strategy** \- Crafting clear, concise text that guides users through the experience  
**Aesthetic appeal** \- Creating a visually pleasing design that aligns with brand identity while prioritizing usability  
**Animations \-** Crafting beautiful yet subtle animations and transitions that make the app feel professional

I need you to take EACH FEATURE below, and give me a cohesive “Functional Brief”. Here’s how I want it formatted. You repeat this for each feature:

\<format\>  
\#\# Feature Name  
\#\#\# Screen X  
\#\#\#\# Screen X State N  
\* description  
\* of  
\* UI & UX  
\* in detail  
\* including animations  
\* any anything else  
\#\#\#\# Screen X State N+1

Repeat for as many N+\# as needed based on the function of the state

\</format\>  
\</task\>

\<feature-list\>

### **User Authentication & Onboarding**

*Secure user registration and login system with basic profile setup to personalize the recipe generation experience.*

* Email/password registration with verification  
* Social login options (Google, Apple)  
* Basic profile setup (dietary restrictions, cooking skill level)  
* Terms acceptance and privacy consent

### **Photo Capture & Upload**

*Camera interface allowing users to photograph restaurant dishes or upload existing photos for recipe generation.*

* Live camera capture with flash/HDR controls  
* Photo gallery upload functionality  
* Image preprocessing and optimization  
* Basic image validation (food detection)

### **Recipe Input Interface**

*User-friendly form for capturing menu descriptions and personal tasting notes to enhance recipe accuracy.*

* Menu description text input with auto-suggestions  
* Tasting notes with guided prompts  
* Optional cuisine type selection  
* Spice level and dietary preference indicators

### **AI Recipe Generation**

*Core AI pipeline that processes images, descriptions, and notes to create detailed, accurate recipes.*

* Multi-modal AI processing (image \+ text)  
* Recipe structure generation (ingredients, steps, techniques)  
* Cooking time and difficulty estimation  
* Serving size calculations

### **Recipe Review & Management**

*Interface for users to view, edit, and organize their generated recipes with social sharing capabilities.*

* Generated recipe display with ingredients and steps  
* Recipe editing and modification requests  
* Personal recipe library with search  
* Social sharing functionality

\</feature-list\>  
\</context\>
```

## Claude Planning Prompts (4).md

```markdown
\<goal\>  
You’re a veteran software engineer (FANG-level) responsible for writing detailed, step-by-step technical specifications for each feature—no real code, only pseudocode where helpful. Ensure every dependency and integration is spelled out clearly.  
\</goal\>

\<format\>  
\`\`\`markdown  
\#\# File System  
\* Frontend/  
  \* …  
\* Backend/  
  \* …

## **Feature Specifications**

### **Feature 1:** 

* **Goal**

   A concise statement of this feature’s purpose.

* **API relationships**

   Which services/endpoints it talks to.

* **Detailed requirements**

  * Requirement A

  * Requirement B

  * …

* **Implementation guide**

  * Pseudocode or sequence diagram

  * Data flow steps

  * Key edge cases

---

### **Feature 2:** 

* **Goal**

* **API relationships**

* **Detailed requirements**

* **Implementation guide**

\</format\>

\<warnings-and-guidelines\>  
1\. \*\*Step-by-step\*\*: Enough detail that a dev can build directly from this.    
2\. \*\*No real code\*\*, only pseudocode where necessary for complex logic.    
3\. For \*\*each feature\*\*, cover:  
   \- \*\*Architecture overview\*\* (diagram, tech-stack justification, deployment)    
   \- \*\*DB schema\*\* (ER diagram, table definitions, indexes, migrations)    
   \- \*\*API design\*\* (endpoints, request/response examples, auth, errors, rate-limit)    
   \- \*\*Frontend structure\*\* (component hierarchy, state mgmt, navigation)    
   \- \*\*CRUD operations\*\* (validation, pagination, soft vs. hard delete)    
   \- \*\*UX flow\*\* (journey maps, wireframes, loading/error states)    
   \- \*\*Security\*\* (auth flow, roles, sanitization, OWASP protections)    
   \- \*\*Testing\*\* (unit, integration, E2E, performance)    
   \- \*\*Data management\*\* (caching, lifecycle, real-time needs)    
   \- \*\*Logging & error handling\*\* (structured logs, alerts, recovery)  
\</warnings-and-guidelines\>

\<context\>

\<features\>  
\</features\>  
\<other-considerations\>  
\</other-considerations\>  
\</context\>

\---
```

## Claude Planning Prompts (5).md

```markdown
\<goal\>  
You’re a veteran software engineer (FANG-level) responsible for writing detailed, step-by-step technical specifications for each feature—no real code, only pseudocode where helpful. Ensure every dependency and integration is spelled out clearly.  
\</goal\>

\<format\>  
\`\`\`markdown  
\#\# File System  
\* Frontend/  
  \* …  
\* Backend/  
  \* …

## **Feature Specifications**

### **Feature 1:** 

* **Goal**

   A concise statement of this feature’s purpose.

* **API relationships**

   Which services/endpoints it talks to.

* **Detailed requirements**

  * Requirement A

  * Requirement B

  * …

* **Implementation guide**

  * Pseudocode or sequence diagram

  * Data flow steps

  * Key edge cases

---

### **Feature 2:** 

* **Goal**

* **API relationships**

* **Detailed requirements**

* **Implementation guide**

\</format\>

\<warnings-and-guidelines\>  
1\. \*\*Step-by-step\*\*: Enough detail that a dev can build directly from this.    
2\. \*\*No real code\*\*, only pseudocode for complex logic.    
3\. For \*\*each feature\*\*, cover:  
   \- \*\*Architecture overview\*\* (diagram, tech-stack justification, deployment)    
   \- \*\*DB schema\*\* (ER diagram, table definitions, indexes, migrations)    
   \- \*\*API design\*\* (endpoints, request/response examples, auth, errors, rate-limit)    
   \- \*\*Frontend structure\*\* (component hierarchy, state mgmt, navigation)    
   \- \*\*CRUD operations\*\* (validation, pagination, soft vs. hard delete)    
   \- \*\*UX flow\*\* (journey maps, wireframes, loading/error states)    
   \- \*\*Security\*\* (auth flow, roles, sanitization, OWASP protections)    
   \- \*\*Testing\*\* (unit, integration, E2E, performance)    
   \- \*\*Data management\*\* (caching, lifecycle, real-time needs)    
   \- \*\*Logging & error handling\*\* (structured logs, alerts, recovery)  
\</warnings-and-guidelines\>

\<context\>

\<features\>

## **MVP Flow**

1. User creates account and completes onboarding  
2. User navigates to camera tab and either takes live photo or uploads existing image  
3. User inputs menu description and personal tasting notes about the dish  
4. System processes image, description, and notes through AI pipeline to generate comprehensive recipe  
5. User reviews generated recipe with ingredients, steps, and cooking techniques  
6. User can save recipe, share with friends, or request modifications before finalizing

---

## **Launch Features (MVP)**

### **User Authentication & Onboarding**

*Secure user registration and login system with basic profile setup to personalize the recipe generation experience.*

* Email/password registration with verification  
* Social login options (Google, Apple)  
* Basic profile setup (dietary restrictions, cooking skill level)  
* Terms acceptance and privacy consent

#### **Tech Involved**

* Supabase Auth  
* React Native authentication flows  
* FastAPI auth middleware

#### **Main Requirements**

* Secure password handling  
* Session management  
* Profile data storage  
* Onboarding flow completion tracking

### **Photo Capture & Upload**

*Camera interface allowing users to photograph restaurant dishes or upload existing photos for recipe generation.*

* Live camera capture with flash/HDR controls  
* Photo gallery upload functionality  
* Image preprocessing and optimization  
* Basic image validation (food detection)

#### **Tech Involved**

* Expo Camera API  
* React Native Image Picker  
* Image compression/optimization libraries  
* Computer vision API for food detection

#### **Main Requirements**

* High-quality image capture  
* Multiple image format support  
* Automatic image optimization  
* Error handling for poor quality images

### **Recipe Input Interface**

*User-friendly form for capturing menu descriptions and personal tasting notes to enhance recipe accuracy.*

* Menu description text input with auto-suggestions  
* Tasting notes with guided prompts  
* Optional cuisine type selection  
* Spice level and dietary preference indicators

#### **Tech Involved**

* React Native form components  
* Input validation libraries  
* Auto-complete functionality  
* Local storage for draft saving

#### **Main Requirements**

* Intuitive form design  
* Input validation and sanitization  
* Draft saving capability  
* Character limits and formatting

### **AI Recipe Generation**

*Core AI pipeline that processes images, descriptions, and notes to create detailed, accurate recipes.*

* Multi-modal AI processing (image \+ text)  
* Recipe structure generation (ingredients, steps, techniques)  
* Cooking time and difficulty estimation  
* Serving size calculations

#### **Tech Involved**

* OpenAI GPT-4V or similar multimodal AI  
* Custom prompt engineering  
* FastAPI background tasks  
* PostgreSQL for recipe storage

#### **Main Requirements**

* High-quality recipe output  
* Processing time under 60 seconds  
* Error handling for AI failures  
* Recipe formatting consistency

### **Recipe Review & Management**

*Interface for users to view, edit, and organize their generated recipes with social sharing capabilities.*

* Generated recipe display with ingredients and steps  
* Recipe editing and modification requests  
* Personal recipe library with search  
* Social sharing functionality

#### **Tech Involved**

* React Native recipe components  
* Supabase real-time subscriptions  
* Share API integration  
* Full-text search implementation

#### **Main Requirements**

* Clean recipe presentation  
* Edit request processing  
* Efficient recipe storage and retrieval  
* Share link generation

\</features\>  
\<other-considerations\>  
Here are the various states of each feature:

**User Authentication & Onboarding**

### **Welcome Screen**

#### **Welcome State**

* Full-screen hero with subtle animated food photography overlay (parallax scroll effect)  
* Clean white background with minimal maroon accent line dividers  
* Centered logo with elegant typography using system fonts (SF Pro on iOS, Roboto on Android)  
* Two primary CTAs: "Sign Up" (filled maroon button) and "Sign In" (outlined maroon button)  
* Typography hierarchy: 32pt app name, 18pt tagline, 16pt CTAs  
* Gentle fade-in animation on load with staggered element reveals (200ms delays)  
* Single "Continue with Demo" ghost button for immediate app exploration

### **Sign Up Screen**

#### **Empty State**

* Minimalist form with floating label inputs on white background  
* Maroon accent color for active input states and focus indicators  
* Email, password, and confirm password fields with real-time validation  
* Password strength indicator using subtle color progression (gray to maroon)  
* Social login buttons with platform-appropriate styling above form  
* 44pt touch targets for accessibility compliance  
* Progress indicator showing step 1 of 3 in top navigation

#### **Form Validation State**

* Inline validation with smooth slide-down error messages below fields  
* Success states show subtle green checkmarks with bounce animation  
* Error states display maroon warning icons with gentle shake animation  
* Disabled submit button until all validations pass, then animates to active state  
* Real-time password matching with visual feedback

#### **Email Verification State**

* Centered illustration with mail icon and confirmation message  
* "Resend Email" button with countdown timer (60 seconds)  
* Clean back navigation to modify email if needed  
* Automatic detection when email is verified with celebratory micro-animation

### **Profile Setup Screen**

#### **Basic Information State**

* Card-based layout with subtle elevation shadows  
* Profile photo upload with circular frame and camera icon overlay  
* Name and location fields with predictive text capabilities  
* Progress indicator showing step 2 of 3  
* "Skip for now" option with ghost button styling  
* Smooth transitions between form fields using focus management

#### **Preference Selection State**

* Grid layout for dietary restrictions with toggle-style selection  
* Cooking skill level slider with visual progress indicators  
* Cuisine preferences using pill-style multi-select components  
* Color-coded selection states using maroon accent system  
* Visual hierarchy through card grouping and section headers  
* Haptic feedback on selections (iOS) with subtle animation confirmations

#### **Completion State**

* Celebration screen with subtle confetti animation  
* Personalized welcome message using entered name  
* "Get Started" CTA leading to main app experience  
* Option to complete profile later in ghost button  
* Smooth transition to tab navigation with slide-up reveal

## **Photo Capture & Upload**

### **Camera Screen**

#### **Default Camera State**

* Full-screen camera viewfinder with minimal UI overlay  
* Semi-transparent black overlay at top/bottom for controls  
* Centered capture button (80pt diameter) with subtle white border  
* Flash toggle, camera flip, and gallery access icons in top corners  
* Bottom drawer hint showing recent photos (swipe up gesture)  
* Grid overlay toggle for composition assistance  
* Focus ring animation on tap-to-focus with maroon accent

#### **Photo Capture State**

* Shutter animation with brief white flash overlay  
* Capture button briefly scales down then up with haptic feedback  
* Thumbnail preview slides in from bottom-right corner  
* "Retake" and "Use Photo" options slide up smoothly  
* Auto-focus indicator before capture with green confirmation  
* Image quality indicator (HD/4K) in top-left corner

#### **Photo Review State**

* Full-screen photo preview with gesture support (pinch-to-zoom, pan)  
* Bottom action sheet with "Retake", "Choose Different", "Continue" options  
* Crop suggestion overlay with intelligent food detection boundaries  
* Image enhancement toggle with before/after preview capability  
* AI confidence indicator for food detection (discrete progress ring)  
* Smooth transitions to next step with slide-left animation

### **Gallery Upload Screen**

#### **Photo Selection State**

* Grid layout showing recent photos with smart food detection highlights  
* Multi-select capability with maroon check overlays  
* Search functionality with AI-powered food photo filtering  
* Album navigation with smooth horizontal scrolling  
* Selected photo counter in top-right with subtle animation  
* "Continue" button slides up from bottom when photos selected

#### **Photo Processing State**

* Selected photos displayed in carousel format  
* Individual edit controls: crop, brightness, contrast  
* AI processing indicator showing food detection confidence  
* Batch processing with progress indicators for multiple photos  
* Option to add more photos with plus button  
* Auto-enhancement suggestion with toggle option

## **Recipe Input Interface**

### **Menu Description Screen**

#### **Text Input State**

* Large, comfortable text area with placeholder guidance  
* Character counter with color progression (gray to maroon at limits)  
* Auto-suggestions panel slides up from bottom as user types  
* Recent searches and common menu items in suggestions  
* Voice input button with visual waveform feedback during recording  
* Smart text formatting for common menu item patterns  
* Predictive text enhanced with restaurant/cuisine context

#### **Auto-Complete State**

* Sliding suggestion panel with categorized recommendations  
* Tap-to-complete with smooth text insertion animations  
* Search history with swipe-to-delete functionality  
* Popular dishes carousel above suggestions  
* Context-aware suggestions based on photo analysis  
* Quick-add chips for common modifiers (spicy, gluten-free, etc.)

### **Tasting Notes Screen**

#### **Guided Prompts State**

* Card-based prompt system with swipe navigation  
* Pre-written prompts: "How did it taste?", "What stood out?", "Texture notes?"  
* Voice-to-text integration with real-time transcription  
* Expandable text areas that grow with content  
* Progress dots showing completion status  
* Skip and previous navigation with smooth card transitions

#### **Detailed Input State**

* Rich text editor with formatting options (bold, italic)  
* Emoji panel for quick flavor descriptors  
* Tag system for categorizing notes (sweet, savory, umami, etc.)  
* Photo annotation tools to mark specific areas  
* Word count with encouraging micro-copy  
* Auto-save functionality with subtle confirmation indicators

### **Enhanced Details Screen**

#### **Cuisine Selection State**

* Visual cuisine picker with representative food photography  
* Popular cuisines in top row, expandable "More" section below  
* Search capability for specific regional cuisines  
* Selection confirmation with smooth color transition  
* Recent selections prioritized for quick access  
* Cultural accuracy considerations in imagery and descriptions

#### **Preferences Configuration State**

* Spice level selector with visual heat indicators  
* Dietary restriction toggles with clear iconography  
* Serving size slider with mathematical preview  
* Cooking time preference range selector  
* Equipment availability checklist  
* Allergen warning system with color-coded alerts

## **AI Recipe Generation**

### **Processing Screen**

#### **Loading State**

* Centered loading animation using custom maroon/white elements  
* Progress stages: "Analyzing image", "Processing notes", "Generating recipe"  
* Estimated time remaining with dynamic updates  
* Food facts or cooking tips displayed during wait  
* Cancel option with confirmation dialog  
* Background blur effect over previous content  
* Subtle particle animation suggesting recipe creation

#### **Progress Updates State**

* Step-by-step progress visualization with checkmarks  
* Real-time status updates with smooth text transitions  
* Percentage complete with animated progress bar  
* Preview snippets of generated content as available  
* Error handling with retry and alternative options  
* Contextual help if processing takes longer than expected

### **Generation Complete Screen**

#### **Success State**

* Celebration micro-animation with recipe preview card slide-up  
* Quick preview showing recipe title and hero image  
* "View Full Recipe" primary CTA with anticipatory hover states  
* Options to regenerate or modify inputs  
* Share immediately button for social platforms  
* Save to library confirmation with subtle animation  
* Rating prompt for AI accuracy feedback

#### **Error State**

* Friendly error messaging with clear next steps  
* Retry options with modified inputs  
* Alternative recipe suggestions based on partial analysis  
* Support contact with contextual error information  
* Back navigation to previous steps with state preservation  
* Offline capability notification if network issues detected

## **Recipe Review & Management**

### **Full Recipe Display Screen**

#### **Complete Recipe State**

* Hero image with gradient overlay for text readability  
* Structured layout: title, description, prep time, difficulty  
* Ingredients list with checkbox functionality and shopping integration  
* Step-by-step instructions with expandable details  
* Nutritional information in collapsible section  
* Cooking tips and chef notes with distinct visual treatment  
* Smooth scrolling with sticky navigation sections

#### **Interactive Elements State**

* Tap-to-scale ingredient quantities based on serving size  
* Timer integration for cooking steps with notifications  
* Progress tracking through recipe steps with visual indicators  
* Note-taking capability with persistent annotations  
* Photo capture for user's cooking progress  
* Voice commands for hands-free navigation during cooking

### **Recipe Editing Screen**

#### **Modification Request State**

* Overlay editing panel with context-aware suggestions  
* Quick modification buttons: "Make Vegetarian", "Less Spicy", "Simpler"  
* Free-form text input for specific changes  
* Visual diff showing proposed vs. original recipe  
* Confirmation step with side-by-side comparison  
* Processing animation for AI-powered modifications  
* Version history for reverting changes

#### **Custom Editing State**

* Inline editing capability for ingredients and steps  
* Drag-and-drop reordering with visual feedback  
* Add/remove ingredients with smooth list animations  
* Rich text editing for step modifications  
* Photo replacement and addition functionality  
* Save states with auto-save and manual save options

### **Recipe Library Screen**

#### **Grid View State**

* Masonry layout optimizing for recipe card images  
* Filter and sort controls in sticky header  
* Search functionality with real-time results  
* Category tabs with swipe navigation  
* Pull-to-refresh with custom loading animation  
* Infinite scroll with progressive loading  
* Empty state with encouraging CTA to create first recipe

#### **List View State**

* Condensed recipe cards with key information  
* Swipe actions for quick operations (share, delete, favorite)  
* Alphabetical index for rapid navigation  
* Recently viewed section at top  
* Favorite recipes prominent placement  
* Batch selection mode for organization operations

### **Social Sharing Screen**

#### **Share Options State**

* Platform-specific sharing with optimized content formats  
* Custom link generation with recipe preview  
* Private sharing via direct message integration  
* Social media story templates with branded overlays  
* Email composition with embedded recipe cards  
* Export options (PDF, print-friendly format)  
* Analytics tracking for shared content engagement

#### **Sharing Success State**

* Confirmation animation with platform-specific feedback  
* Option to share to additional platforms  
* Track sharing engagement with recipient interactions  
* Follow-up prompts for user feedback on shared recipes  
* Recent shares history with performance metrics  
* Thank you messaging with community building elements

\</other-considerations\>  
\</context\>

\---
```

## Claude Planning Prompts (1).md

```markdown
\<goal\>

You’re a Senior Software Engineer with extensive experience designing large-scale web and mobile applications. Help me brainstorm the overall structure of my new project, thinking through:

\- I’m building an app to help unemployed IBEW Journymen navigate and consolidate and archaic job board system.

\- It’s for Journeymen who want to travel for work.  

\- It matters to Journeymen because there are too many inconsistant and contraditory rules making it frustrating and difficult many members simply quite trying.  

\- It’s different from existing solutions because it was created by a Journeyman for Journeymen 

\</goal\>

\<format\>

Return only this Markdown structure—no extra preamble or sign-off.

\#\# MVP Flow

A clear, step-by-step description of the core minimum-viable process:

1. **Job Search and Listings**: How important is it for you to have a comprehensive job search feature? What specific functionalities would you expect?
2. **Profile Management**: How critical is it to have a detailed profile management feature? What information should be included?
3. **Application Tracking**: How helpful would it be to have an application tracking feature? What statuses and details would you like to see?
4. **Notifications/Alerts**: How do you prefer to receive notifications about new job postings or application updates?
5. **Local Union Information**: How useful would it be to have detailed information about local union halls and their referral policies in the app?
6. **User Experience**: What are your expectations for the overall user experience in terms of ease of use and navigation?

\---

\#\# Launch Features (MVP)

\#\#\# Feature Name

2–3-sentence summary of what this feature does

\* Core requirement 1  

\* Core requirement 2  

\* …

\#\#\#\# Tech Involved

\* Tech or service 1  

\* Tech or service 2  

\#\#\#\# Main Requirements

\* Requirement 1  

\* Requirement 2  

\---

\#\# Future Features (Post-MVP)

\#\#\# Feature Name

\* Core requirement 1  

\* Core requirement 2  

\* …

\#\#\#\# Tech Involved

\* Tech or service 1  

\* Tech or service 2  

\#\#\#\# Main Requirements

\* Requirement 1  

\* Requirement 2  

\---

\#\# System Diagram

A clean, color-coded SVG architecture diagram showing all MVP components and their relationships.

\---

\#\# Questions & Clarifications

\* Question 1  

\* Question 2  

\* …

\---

\#\# Architecture Consideration Questions

\* Consideration 1  

\* Consideration 2  

\* …

\</format\>

\<warnings-or-guidance\>

\- Focus on functional feature goals, not detailed UI/UX.  

\- If anything is ambiguous, ask for clarification.  

\- Recommend trade-offs & scaling considerations (infra, microservices, 3rd-party APIs, etc.).  

\- Include hosting/CI-CD/monitoring thoughts where relevant.

\</warnings-or-guidance\>

\<context\>

\<audience-notes\>

WHAT \- I’m building an app to help unemployed IBEW Journymen navigate and consolidate and archaic job board system.

WHO \- It’s for Journeymen who want to travel for work.

WHY \- It matters to Journeymen because there are too many inconsistant and contraditory rules making it frustrating and difficult many members simply quite trying.

HOW \- It’s different from existing solutions because it was created by a Journeyman for Journeymen.

\</audience-notes\>

\<mvp-flow\>

Here is the full extent of how the app should function as an MVP:

User registers for app

1. **Job Search and Listings**: How important is it for you to have a comprehensive job search feature? What specific functionalities would you expect?
2. **Profile Management**: How critical is it to have a detailed profile management feature? What information should be included?
3. **Notifications/Alerts**: How do you prefer to receive notifications about new job postings or application updates?
4. **Local Union Information**: How useful would it be to have detailed information about local union halls and their referral policies in the app?

\</mvp-flow\>

\<current-tech-choices\>

The frontend will be built in React Native using Expo

The backend will be built in Python using FastAPI

The database will be nosql, hosted through Firebase

The authentication will use Firebase

Use SQLAlchemy as an ORM with Alembic for migrations

Recommend me a deployment solution for the backend

I’d like to develop locally with a Docker container

I will use Posthog for analytics in my application

I will use Stripe for payments in my application

\</current-tech-choices\>

\</context\>
```

## Claude Planning Prompts.md

```markdown
\<goal\>  
You’re a Senior Software Engineer with extensive experience designing large-scale web and mobile applications. Help me brainstorm the overall structure of my new project, thinking through:  
\- \*\*WHAT\*\* I’m building    
\- \*\*WHO\*\* it’s for    
\- \*\*WHY\*\* it matters    
\- \*\*HOW\*\* it’s different from existing solutions    
\</goal\>

\<format\>  
Return \*\*only\*\* this Markdown structure—no extra preamble or sign-off.

\#\# MVP Flow  
A clear, step-by-step description of the core minimum-viable process:  
1\. Step 1    
2\. Step 2    
3\. …

\---

\#\# Launch Features (MVP)  
\#\#\# Feature Name  
\_2–3-sentence summary of what this feature does\_

\* Core requirement 1    
\* Core requirement 2    
\* …

\#\#\#\# Tech Involved  
\* Tech or service 1    
\* Tech or service 2  

\#\#\#\# Main Requirements  
\* Requirement 1    
\* Requirement 2  

\---

\#\# Future Features (Post-MVP)  
\#\#\# Feature Name  
\* Core requirement 1    
\* Core requirement 2    
\* …

\#\#\#\# Tech Involved  
\* Tech or service 1    
\* Tech or service 2  

\#\#\#\# Main Requirements  
\* Requirement 1    
\* Requirement 2  

\---

\#\# System Diagram  
A clean, color-coded SVG architecture diagram showing all MVP components and their relationships.

\---

\#\# Questions & Clarifications  
\* Question 1    
\* Question 2    
\* …

\---

\#\# Architecture Consideration Questions  
\* Consideration 1    
\* Consideration 2    
\* …  
\</format\>

\<warnings-or-guidance\>  
\- Focus on \*\*functional\*\* feature goals, not detailed UI/UX.    
\- If anything is ambiguous, ask for clarification.    
\- Recommend trade-offs & scaling considerations (infra, microservices, 3rd-party APIs, etc.).    
\- Include hosting/CI-CD/monitoring thoughts where relevant.  
\</warnings-or-guidance\>

\<context\>  
\> \*\*Fill in here:\*\*    
\> • Project overview & goals (WHAT/WHO/WHY/HOW)    
\> • Core MVP Flow (the numbered steps)    
\> • Any existing tech choices or constraints    
\> • Anything else the architect should know before starting    
\</context\>
```

## Statistics

- Total Files: 6
- Total Characters: 37393
- Total Tokens: 0
