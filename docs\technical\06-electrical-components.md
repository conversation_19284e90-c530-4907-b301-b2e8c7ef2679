# Electrical Components Library

*Comprehensive documentation for industry-specific electrical UI components*

## ⚡ Overview

The Electrical Components Library provides a comprehensive collection of Flutter widgets designed specifically for electrical industry applications. These components incorporate authentic electrical industry aesthetics, terminology, and user interaction patterns familiar to IBEW electrical workers.

## 🎨 Design Philosophy

### Visual Language

- **Authentic Industry Aesthetics**: Components mirror real electrical equipment and interfaces
- **High Contrast**: Optimized for outdoor visibility and safety glasses
- **Copper & Navy Theme**: Professional color scheme reflecting electrical industry traditions
- **Safety-First Design**: Clear visual indicators and fail-safe interaction patterns

### Animation Principles

- **Physics-Based**: Animations follow real-world electrical phenomena
- **Performance Optimized**: Smooth 60fps animations with minimal battery impact
- **Purposeful Motion**: Every animation serves a functional purpose
- **Electrical Context**: Movements reflect electrical flow, switching, and power states

## 🔧 Component Categories

## Loading & Progress Components

### ThreePhaseSineWaveLoader

Animated visualization of 3-phase AC power systems.

**Purpose**: Display electrical power flow representation during loading states
**Best Used For**: Job data loading, system initialization, power system operations

```dart
ThreePhaseSineWaveLoader(
  width: 200,
  height: 60,
  duration: Duration(milliseconds: 2000),
  phaseColors: [
    AppTheme.errorRed,      // Phase A
    AppTheme.white,         // Phase B  
    AppTheme.infoBlue,      // Phase C
  ],
  showLabels: true,
  amplitude: 20.0,
)
```

**Properties**:

- `width/height`: Size constraints for the component
- `duration`: Complete animation cycle duration
- `phaseColors`: Colors for the three phases (A, B, C)
- `showLabels`: Display phase labels (A, B, C)
- `amplitude`: Wave height amplitude
- `frequency`: Electrical frequency (default 60Hz for North America)

**Animation Details**:

- Three sine waves with 120° phase separation
- Smooth continuous animation representing AC power flow
- Configurable frequency and amplitude
- Phase-accurate electrical representation

### ElectricalRotationMeter

Professional gauge-style loading indicator resembling electrical meters.

**Purpose**: Loading indicator for professional electrical contexts
**Best Used For**: System diagnostics, meter readings, performance monitoring

```dart
ElectricalRotationMeter(
  size: 120,
  label: 'Calculating Load...',
  duration: Duration(milliseconds: 3000),
  needleColor: AppTheme.accentCopper,
  dialColor: AppTheme.primaryNavy,
  showTicks: true,
  sweepAngle: 270, // 3/4 circle like real meters
)
```

**Properties**:

- `size`: Diameter of the meter gauge
- `label`: Optional text label below meter
- `duration`: Full rotation duration
- `needleColor`: Meter needle color
- `dialColor`: Meter background color
- `showTicks`: Display measurement tick marks
- `sweepAngle`: Arc angle (270° standard for electrical meters)

**Animation Details**:

- Realistic needle movement with inertia
- Tick marks positioned at standard electrical meter intervals
- Smooth acceleration and deceleration curves
- Optional electrical unit labels (V, A, kW, etc.)

### PowerLineLoader

Transmission line-inspired loading animation with energy flow.

**Purpose**: Represent electrical transmission and power distribution
**Best Used For**: Network operations, data synchronization, storm work alerts

```dart
PowerLineLoader(
  width: 300,
  height: 80,
  duration: Duration(milliseconds: 1500),
  towerCount: 3,
  energyColor: AppTheme.accentCopper,
  showSparks: true,
  flowDirection: PowerFlowDirection.leftToRight,
)
```

**Properties**:

- `width/height`: Component dimensions
- `duration`: Energy pulse cycle duration
- `towerCount`: Number of transmission towers
- `energyColor`: Color of energy flow animation
- `showSparks`: Display electrical spark effects
- `flowDirection`: Direction of energy flow animation

**Animation Details**:

- Realistic transmission tower illustrations
- Pulsing energy flow along power lines
- Optional spark effects at connection points
- Configurable flow direction and speed

## Interactive Components

### CircuitBreakerToggle

Professional circuit breaker-styled toggle switch.

**Purpose**: Primary switching control with electrical industry styling
**Best Used For**: System enable/disable, filter activation, safety controls

```dart
CircuitBreakerToggle(
  isOn: _systemEnabled,
  onChanged: (value) => setState(() => _systemEnabled = value),
  width: 80,
  height: 40,
  onColor: AppTheme.successGreen,
  offColor: AppTheme.mediumGray,
  switchDuration: Duration(milliseconds: 200),
  showLabels: true, // "I" for ON, "O" for OFF
)
```

**Properties**:

- `isOn`: Current toggle state
- `onChanged`: Callback for state changes
- `width/height`: Switch dimensions
- `onColor/offColor`: Colors for respective states
- `switchDuration`: Animation duration for state change
- `showLabels`: Display standard electrical symbols

**Animation Details**:

- Smooth switching animation with electrical snap-action feel
- Standard electrical symbols (I/O) for international recognition
- Haptic feedback integration for tactile response
- LED-style indicator for current state

### JJCircuitBreakerSwitch

Advanced circuit breaker with multiple states and safety features.

**Purpose**: Professional-grade switching with safety lockouts
**Best Used For**: Critical system controls, safety interlocks, emergency stops

```dart
JJCircuitBreakerSwitch(
  state: CircuitBreakerState.on,
  onStateChanged: _handleBreakerChange,
  hasFault: false,
  isLocked: false,
  showCurrent: true,
  currentValue: 125.5,
  ratedCurrent: 200.0,
  size: CircuitBreakerSize.standard,
)
```

**States**:

- `CircuitBreakerState.on`: Energized and conducting
- `CircuitBreakerState.off`: De-energized and open
- `CircuitBreakerState.tripped`: Fault condition
- `CircuitBreakerState.locked`: Safety lockout engaged

**Properties**:

- `state`: Current breaker state
- `onStateChanged`: State change callback
- `hasFault`: Fault condition indicator
- `isLocked`: Safety lockout status
- `showCurrent`: Display current measurement
- `currentValue/ratedCurrent`: Electrical ratings

## Specialized Training Components

### Transformer Trainer System

Interactive educational component for transformer bank configurations.

**Purpose**: Training and reference for electrical transformer connections
**Best Used For**: Educational content, technical reference, troubleshooting guides

```dart
JJTransformerTrainer(
  bankType: TransformerBankType.wyeWye,
  mode: TrainerMode.interactive,
  showVoltages: true,
  enableQuizMode: true,
  onConfigurationComplete: _handleTrainingComplete,
  difficulty: TrainingDifficulty.journeyman,
)
```

**Bank Types**:

- `TransformerBankType.wyeWye`: Y-Y configuration
- `TransformerBankType.deltaDelta`: Δ-Δ configuration
- `TransformerBankType.wyeDelta`: Y-Δ configuration
- `TransformerBankType.deltaWye`: Δ-Y configuration
- `TransformerBankType.openDelta`: Open delta configuration

**Modes**:

- `TrainerMode.demonstration`: Animated demonstration
- `TrainerMode.interactive`: User interaction required
- `TrainerMode.quiz`: Assessment and testing
- `TrainerMode.reference`: Static reference diagram

**Educational Features**:

- Interactive connection points
- Voltage vector diagrams
- Phase relationship indicators
- Real-world application examples
- Progressive difficulty levels

### Connection Point Widget

Interactive connection terminal for transformer training.

```dart
ConnectionPoint(
  position: Offset(100, 50),
  isConnected: false,
  connectionType: ConnectionType.primary,
  voltage: 4160.0,
  phase: ElectricalPhase.A,
  onConnectionChanged: _handleConnection,
  showVoltage: true,
  isHighlighted: false,
)
```

**Properties**:

- `position`: Screen coordinates for placement
- `isConnected`: Connection state
- `connectionType`: Primary, secondary, or neutral
- `voltage`: Voltage level for display
- `phase`: Electrical phase (A, B, C, or neutral)
- `onConnectionChanged`: Connection callback
- `showVoltage`: Display voltage label
- `isHighlighted`: Visual emphasis state

## Icon and Visual Components

### HardHatIcon

Safety-themed icon representing protective equipment.

**Purpose**: Safety indicators and personal protective equipment representation
**Best Used For**: Safety reminders, profile indicators, certification displays

```dart
HardHatIcon(
  size: 48,
  color: AppTheme.warningYellow,
  hasReflectiveStripe: true,
  condition: HardHatCondition.excellent,
  showCertification: true,
)
```

**Properties**:

- `size`: Icon dimensions
- `color`: Hard hat color (often safety colors)
- `hasReflectiveStripe`: Show reflective safety stripe
- `condition`: Visual wear condition
- `showCertification`: Display safety certification mark

### TransmissionTowerIcon

Electrical infrastructure representation.

**Purpose**: Electrical transmission and distribution infrastructure
**Best Used For**: System diagrams, location markers, infrastructure status

```dart
TransmissionTowerIcon(
  size: 64,
  color: AppTheme.primaryNavy,
  voltage: VoltageLevel.transmission,
  hasPowerLines: true,
  status: TowerStatus.energized,
)
```

**Properties**:

- `size`: Icon dimensions
- `color`: Tower structure color
- `voltage`: Voltage classification (distribution/transmission)
- `hasPowerLines`: Show attached power lines
- `status`: Operational status (energized/de-energized/faulted)

## Animation Performance Optimization

### Battery-Efficient Animations

All electrical components include battery optimization features:

```dart
class BatteryEfficientAnimations {
  static AnimationController createOptimizedController({
    required Duration duration,
    required TickerProvider vsync,
    bool respectPowerSaveMode = true,
  }) {
    // Reduce animation frame rate in power save mode
    final effectiveDuration = respectPowerSaveMode && 
        _isPowerSaveMode() ? duration * 1.5 : duration;
    
    return AnimationController(
      duration: effectiveDuration,
      vsync: vsync,
    );
  }
  
  static bool _isPowerSaveMode() {
    // Check device power save settings
    return false; // Implement platform-specific check
  }
}
```

### Performance Monitoring

Components include built-in performance monitoring:

```dart
class TransformerPerformanceMonitor {
  static final Map<String, Duration> _renderTimes = {};
  
  static void recordRenderTime(String componentName, Duration renderTime) {
    _renderTimes[componentName] = renderTime;
    
    // Alert if render time exceeds threshold
    if (renderTime.inMilliseconds > 16) { // 60fps threshold
      debugPrint('Performance warning: $componentName took ${renderTime.inMilliseconds}ms');
    }
  }
}
```

## Accessibility Features

### Screen Reader Support

All components include comprehensive accessibility features:

```dart
class ElectricalAccessibility {
  static String describeBreakerState(CircuitBreakerState state) {
    return switch (state) {
      CircuitBreakerState.on => 'Circuit breaker is ON and energized',
      CircuitBreakerState.off => 'Circuit breaker is OFF and safe',
      CircuitBreakerState.tripped => 'Circuit breaker has TRIPPED due to fault',
      CircuitBreakerState.locked => 'Circuit breaker is LOCKED OUT for safety',
    };
  }
  
  static String describeVoltageLevel(double voltage) {
    if (voltage < 1000) {
      return 'Low voltage: ${voltage.toStringAsFixed(0)} volts';
    } else if (voltage < 35000) {
      return 'Medium voltage: ${(voltage / 1000).toStringAsFixed(1)} kilovolts';
    } else {
      return 'High voltage: ${(voltage / 1000).toStringAsFixed(0)} kilovolts';
    }
  }
}
```

### High Contrast Mode

Components adapt to system accessibility settings:

```dart
class ElectricalThemeData {
  static ThemeData getAccessibleTheme(BuildContext context) {
    final isHighContrast = MediaQuery.of(context).highContrast;
    
    return AppTheme.lightTheme.copyWith(
      colorScheme: isHighContrast 
        ? _highContrastColorScheme 
        : AppTheme.lightTheme.colorScheme,
    );
  }
  
  static const ColorScheme _highContrastColorScheme = ColorScheme.light(
    primary: Colors.black,
    secondary: Colors.white,
    error: Color(0xFFB71C1C), // High contrast red
    // ... other high contrast colors
  );
}
```

## Integration Patterns

### Using Components in Screens

```dart
class JobsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('IBEW Jobs'),
        actions: [
          CircuitBreakerToggle(
            isOn: context.watch<JobFilterProvider>().isStormWorkEnabled,
            onChanged: (value) {
              context.read<JobFilterProvider>().toggleStormWork(value);
            },
            width: 60,
            height: 30,
          ),
        ],
      ),
      body: Consumer<JobFilterProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: ElectricalRotationMeter(
                label: 'Searching for jobs...',
                size: 80,
              ),
            );
          }
          
          return ListView.builder(
            itemCount: provider.filteredJobs.length,
            itemBuilder: (context, index) {
              final job = provider.filteredJobs[index];
              return JobCard(
                job: job,
                isStormWork: job.typeOfWork?.contains('storm') ?? false,
              );
            },
          );
        },
      ),
    );
  }
}
```

### Animation Coordination

```dart
class CoordinatedElectricalAnimations extends StatefulWidget {
  @override
  _CoordinatedElectricalAnimationsState createState() => _CoordinatedElectricalAnimationsState();
}

class _CoordinatedElectricalAnimationsState extends State<CoordinatedElectricalAnimations>
    with TickerProviderStateMixin {
  late AnimationController _powerFlowController;
  late AnimationController _meterController;
  
  @override
  void initState() {
    super.initState();
    
    // Synchronized animations for realistic electrical system behavior
    _powerFlowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _meterController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    
    // Start meter animation after power flow begins
    _powerFlowController.addStatusListener((status) {
      if (status == AnimationStatus.forward) {
        Future.delayed(const Duration(milliseconds: 500), () {
          _meterController.forward();
        });
      }
    });
    
    _powerFlowController.repeat();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        PowerLineLoader(
          width: 300,
          height: 80,
          controller: _powerFlowController,
        ),
        const SizedBox(height: 20),
        ElectricalRotationMeter(
          size: 120,
          controller: _meterController,
          label: 'System Load',
        ),
      ],
    );
  }
}
```

## Testing Components

### Widget Testing Patterns

```dart
void main() {
  group('CircuitBreakerToggle Tests', () {
    testWidgets('should display OFF state initially', (tester) async {
      bool currentState = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CircuitBreakerToggle(
              isOn: currentState,
              onChanged: (value) => currentState = value,
            ),
          ),
        ),
      );
      
      // Verify OFF state visuals
      expect(find.text('O'), findsOneWidget);
      expect(find.text('I'), findsNothing);
    });
    
    testWidgets('should toggle state when tapped', (tester) async {
      bool currentState = false;
      
      await tester.pumpWidget(
        StatefulBuilder(
          builder: (context, setState) {
            return MaterialApp(
              home: Scaffold(
                body: CircuitBreakerToggle(
                  isOn: currentState,
                  onChanged: (value) => setState(() => currentState = value),
                ),
              ),
            );
          },
        ),
      );
      
      // Tap the toggle
      await tester.tap(find.byType(CircuitBreakerToggle));
      await tester.pumpAndSettle();
      
      // Verify state changed
      expect(find.text('I'), findsOneWidget);
      expect(currentState, isTrue);
    });
  });
}
```

### Performance Testing

```dart
void main() {
  group('Animation Performance Tests', () {
    testWidgets('ThreePhaseSineWaveLoader should maintain 60fps', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ThreePhaseSineWaveLoader(
              width: 200,
              height: 60,
            ),
          ),
        ),
      );
      
      // Measure frame render times
      final stopwatch = Stopwatch()..start();
      
      for (int i = 0; i < 60; i++) {
        await tester.pump(const Duration(milliseconds: 16)); // 60fps
      }
      
      stopwatch.stop();
      
      // Should complete 60 frames in approximately 1 second
      expect(stopwatch.elapsedMilliseconds, lessThan(1100));
    });
  });
}
```

---

*These electrical components provide authentic industry styling while maintaining Flutter performance standards and accessibility requirements.*
