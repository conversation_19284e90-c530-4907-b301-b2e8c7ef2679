{"permissions": {"allow": ["WebFetch(domain:github.com)", "Bash(find:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(npm install:*)", "<PERSON><PERSON>(git clone:*)", "Bash(cp:*)", "mcp__serena__find_file", "mcp__serena__list_dir", "mcp__serena__activate_project", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__find_symbol", "mcp__serena__get_symbols_overview", "mcp__serena__think_about_collected_information", "mcp__serena__write_memory", "mcp__serena__think_about_task_adherence", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__read_memory", "mcp__serena__search_for_pattern", "mcp__serena__list_memories", "Bash(flutter analyze:*)", "Bash(grep:*)", "multiEdit", "webFetch", "task", "todo", "webSearch", "Bash(rg:*)", "<PERSON><PERSON>(cat:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["brave-search", "search1api-crawler", "Bright Data", "mcp-server-fetch", "universal_mcp", "Framelink Figma MCP", "server-memory", "firecrawl-mcp", "task-master-ai", "sequential-thinking", "think-mcp-server", "mcp-server-deep-research", "@21st-dev/magic"]}