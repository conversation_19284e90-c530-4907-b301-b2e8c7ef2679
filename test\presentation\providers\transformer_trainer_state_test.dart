import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../lib/electrical_components/transformer_trainer/state/transformer_state.dart';
import '../../../lib/electrical_components/transformer_trainer/models/transformer_models.dart';

void main() {
  group('TransformerTrainerState Tests', () {
    late TransformerTrainerState trainerState;

    setUp(() {
      trainerState = TransformerTrainerState();
    });

    test('initializes with default state', () {
      expect(trainerState.currentState.bankType, equals(TransformerBankType.wyeToWye));
      expect(trainerState.currentState.mode, equals(TrainingMode.guided));
      expect(trainerState.currentState.difficulty, equals(DifficultyLevel.beginner));
      expect(trainerState.currentState.currentStep, equals(0));
      expect(trainerState.currentState.connections, isEmpty);
      expect(trainerState.currentState.isComplete, isFalse);
      expect(trainerState.currentState.errors, isEmpty);
    });

    test('updates state and notifies listeners', () {
      bool wasNotified = false;
      trainerState.addListener(() {
        wasNotified = true;
      });

      final newState = TrainingState(
        bankType: TransformerBankType.deltaToWye,
        mode: TrainingMode.quiz,
        difficulty: DifficultyLevel.intermediate,
      );

      trainerState.updateState(newState);

      expect(trainerState.currentState.bankType, equals(TransformerBankType.deltaToWye));
      expect(trainerState.currentState.mode, equals(TrainingMode.quiz));
      expect(trainerState.currentState.difficulty, equals(DifficultyLevel.intermediate));
      expect(wasNotified, isTrue);
    });

    test('adds connection and updates state', () {
      final connection = WireConnection(
        from: const ConnectionPoint(
          id: 'H1',
          label: 'H1',
          type: ConnectionType.primary,
          position: Offset(0, 0),
          isInput: true,
        ),
        to: const ConnectionPoint(
          id: 'X1',
          label: 'X1',
          type: ConnectionType.secondary,
          position: Offset(100, 100),
          isInput: false,
        ),
        isCorrect: true,
      );

      trainerState.addConnection(connection);

      expect(trainerState.currentState.connections, contains(connection));
      expect(trainerState.currentState.connections.length, equals(1));
    });

    test('removes connection correctly', () {
      final connection1 = WireConnection(
        from: const ConnectionPoint(
          id: 'H1',
          label: 'H1',
          type: ConnectionType.primary,
          position: Offset(0, 0),
          isInput: true,
        ),
        to: const ConnectionPoint(
          id: 'X1',
          label: 'X1',
          type: ConnectionType.secondary,
          position: Offset(100, 100),
          isInput: false,
        ),
      );

      final connection2 = WireConnection(
        from: const ConnectionPoint(
          id: 'H2',
          label: 'H2',
          type: ConnectionType.primary,
          position: Offset(0, 50),
          isInput: true,
        ),
        to: const ConnectionPoint(
          id: 'X2',
          label: 'X2',
          type: ConnectionType.secondary,
          position: Offset(100, 150),
          isInput: false,
        ),
      );

      trainerState.addConnection(connection1);
      trainerState.addConnection(connection2);
      expect(trainerState.currentState.connections.length, equals(2));

      trainerState.removeConnection('H1');
      expect(trainerState.currentState.connections.length, equals(1));
      expect(trainerState.currentState.connections, isNot(contains(connection1)));
      expect(trainerState.currentState.connections, contains(connection2));
    });

    test('resets state to initial values', () {
      // Modify state
      final connection = WireConnection(
        from: const ConnectionPoint(
          id: 'H1',
          label: 'H1',
          type: ConnectionType.primary,
          position: Offset(0, 0),
          isInput: true,
        ),
        to: const ConnectionPoint(
          id: 'X1',
          label: 'X1',
          type: ConnectionType.secondary,
          position: Offset(100, 100),
          isInput: false,
        ),
      );

      trainerState.addConnection(connection);
      trainerState.nextStep();
      trainerState.addError('Test error');

      expect(trainerState.currentState.connections, isNotEmpty);
      expect(trainerState.currentState.currentStep, equals(1));
      expect(trainerState.currentState.errors, isNotEmpty);

      // Reset
      trainerState.resetState();

      expect(trainerState.currentState.connections, isEmpty);
      expect(trainerState.currentState.currentStep, equals(0));
      expect(trainerState.currentState.errors, isEmpty);
      expect(trainerState.currentState.isComplete, isFalse);
    });

    test('advances to next step', () {
      expect(trainerState.currentState.currentStep, equals(0));

      trainerState.nextStep();
      expect(trainerState.currentState.currentStep, equals(1));

      trainerState.nextStep();
      expect(trainerState.currentState.currentStep, equals(2));
    });

    test('goes to previous step', () {
      trainerState.nextStep();
      trainerState.nextStep();
      expect(trainerState.currentState.currentStep, equals(2));

      trainerState.previousStep();
      expect(trainerState.currentState.currentStep, equals(1));

      trainerState.previousStep();
      expect(trainerState.currentState.currentStep, equals(0));

      // Should not go below 0
      trainerState.previousStep();
      expect(trainerState.currentState.currentStep, equals(0));
    });

    test('marks training as complete', () {
      expect(trainerState.currentState.isComplete, isFalse);

      trainerState.markComplete();
      expect(trainerState.currentState.isComplete, isTrue);
    });

    test('adds and clears errors', () {
      expect(trainerState.currentState.errors, isEmpty);

      trainerState.addError('Connection error');
      expect(trainerState.currentState.errors, contains('Connection error'));
      expect(trainerState.currentState.errors.length, equals(1));

      trainerState.addError('Timing error');
      expect(trainerState.currentState.errors.length, equals(2));

      trainerState.clearErrors();
      expect(trainerState.currentState.errors, isEmpty);
    });

    test('validates connections correctly', () {
      final correctConnection = WireConnection(
        from: const ConnectionPoint(
          id: 'H1',
          label: 'H1',
          type: ConnectionType.primary,
          position: Offset(0, 0),
          isInput: true,
        ),
        to: const ConnectionPoint(
          id: 'X1',
          label: 'X1',
          type: ConnectionType.secondary,
          position: Offset(100, 100),
          isInput: false,
        ),
        isCorrect: true,
      );

      final incorrectConnection = WireConnection(
        from: const ConnectionPoint(
          id: 'H1',
          label: 'H1',
          type: ConnectionType.primary,
          position: Offset(0, 0),
          isInput: true,
        ),
        to: const ConnectionPoint(
          id: 'X2',
          label: 'X2',
          type: ConnectionType.secondary,
          position: Offset(100, 150),
          isInput: false,
        ),
        isCorrect: false,
      );

      trainerState.addConnection(correctConnection);
      trainerState.addConnection(incorrectConnection);

      final isValid = trainerState.validateConnections();
      
      // Should be false because one connection is incorrect
      expect(isValid, isFalse);
    });

    test('validates all correct connections', () {
      final connection1 = WireConnection(
        from: const ConnectionPoint(
          id: 'H1',
          label: 'H1',
          type: ConnectionType.primary,
          position: Offset(0, 0),
          isInput: true,
        ),
        to: const ConnectionPoint(
          id: 'X1',
          label: 'X1',
          type: ConnectionType.secondary,
          position: Offset(100, 100),
          isInput: false,
        ),
        isCorrect: true,
      );

      final connection2 = WireConnection(
        from: const ConnectionPoint(
          id: 'H2',
          label: 'H2',
          type: ConnectionType.primary,
          position: Offset(0, 50),
          isInput: true,
        ),
        to: const ConnectionPoint(
          id: 'X2',
          label: 'X2',
          type: ConnectionType.secondary,
          position: Offset(100, 150),
          isInput: false,
        ),
        isCorrect: true,
      );

      trainerState.addConnection(connection1);
      trainerState.addConnection(connection2);

      final isValid = trainerState.validateConnections();
      expect(isValid, isTrue);
    });

    test('changes bank type correctly', () {
      expect(trainerState.currentState.bankType, equals(TransformerBankType.wyeToWye));

      trainerState.changeBankType(TransformerBankType.deltaToWye);
      expect(trainerState.currentState.bankType, equals(TransformerBankType.deltaToWye));
    });

    test('changes training mode correctly', () {
      expect(trainerState.currentState.mode, equals(TrainingMode.guided));

      trainerState.changeMode(TrainingMode.quiz);
      expect(trainerState.currentState.mode, equals(TrainingMode.quiz));
    });

    test('changes difficulty level correctly', () {
      expect(trainerState.currentState.difficulty, equals(DifficultyLevel.beginner));

      trainerState.changeDifficulty(DifficultyLevel.advanced);
      expect(trainerState.currentState.difficulty, equals(DifficultyLevel.advanced));
    });

    test('notifies listeners on all state changes', () {
      int notificationCount = 0;
      trainerState.addListener(() {
        notificationCount++;
      });

      trainerState.changeBankType(TransformerBankType.deltaToDelta);
      trainerState.changeMode(TrainingMode.quiz);
      trainerState.changeDifficulty(DifficultyLevel.intermediate);
      trainerState.nextStep();
      trainerState.markComplete();

      expect(notificationCount, equals(5));
    });

    test('handles duplicate connections correctly', () {
      final connection = WireConnection(
        from: const ConnectionPoint(
          id: 'H1',
          label: 'H1',
          type: ConnectionType.primary,
          position: Offset(0, 0),
          isInput: true,
        ),
        to: const ConnectionPoint(
          id: 'X1',
          label: 'X1',
          type: ConnectionType.secondary,
          position: Offset(100, 100),
          isInput: false,
        ),
      );

      trainerState.addConnection(connection);
      expect(trainerState.currentState.connections.length, equals(1));

      // Try to add the same connection again (based on connection point IDs)
      trainerState.addConnection(connection);
      
      // Should still only have one connection (no duplicates)
      expect(trainerState.currentState.connections.length, equals(1));
    });

    test('calculates progress correctly', () {
      expect(trainerState.getProgress(), equals(0.0));

      // Assuming we have 5 total steps for testing
      trainerState.nextStep(); // Step 1
      expect(trainerState.getProgress(), greaterThan(0.0));
      expect(trainerState.getProgress(), lessThan(1.0));

      trainerState.markComplete();
      expect(trainerState.getProgress(), equals(1.0));
    });
  });
}