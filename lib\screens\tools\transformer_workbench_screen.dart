import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../design_system/app_theme.dart';
import '../../models/transformer_models.dart';
import '../../widgets/connection_point.dart';

/// Interactive transformer workbench screen for hands-on training
class TransformerWorkbenchScreen extends StatefulWidget {
  final TransformerBankType bankType;
  final TrainingMode mode;
  final DifficultyLevel difficulty;
  final bool isReferenceMode;

  const TransformerWorkbenchScreen({
    super.key,
    required this.bankType,
    required this.mode,
    required this.difficulty,
    this.isReferenceMode = false,
  });

  @override
  State<TransformerWorkbenchScreen> createState() => _TransformerWorkbenchScreenState();
}

class _TransformerWorkbenchScreenState extends State<TransformerWorkbenchScreen>
    with TickerProviderStateMixin {
  
  // Animation controllers
  late AnimationController _successAnimationController;
  late AnimationController _hintAnimationController;
  late Animation<double> _successAnimation;
  late Animation<double> _hintAnimation;

  // Training state

  // Connection state
  final List<WireConnection> _connections = [];
  final List<ConnectionPoint> _connectionPoints = [];
  String? _selectedConnectionPointId;
  ConnectionMode _connectionMode = ConnectionMode.stickyKeys;
  
  // UI state
  bool _showHints = false;
  bool _showValidation = false;
  String? _currentHint;
  String? _validationMessage;
  Color _selectedWireColor = Colors.red;
  String _selectedPhase = 'A';
  
  // Wire colors for different phases
  final Map<String, Color> _wireColors = {
    'A': Colors.red,
    'B': Colors.blue,
    'C': Colors.yellow,
    'N': Colors.grey,
    'G': Colors.green,
  };

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeTrainingState();
    _generateConnectionPoints();
  }

  void _initializeAnimations() {
    _successAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _hintAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _successAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _successAnimationController, curve: Curves.elasticOut),
    );
    _hintAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _hintAnimationController, curve: Curves.easeInOut),
    );
  }

  void _initializeTrainingState() {
  }

  void _generateConnectionPoints() {
    _connectionPoints.clear();
    
    // Generate connection points based on transformer bank type
    switch (widget.bankType) {
      case TransformerBankType.wyeToWye:
        _generateWyeWyeConnectionPoints();
        break;
      case TransformerBankType.deltaToDelta:
        _generateDeltaDeltaConnectionPoints();
        break;
      case TransformerBankType.wyeToDelta:
        _generateWyeDeltaConnectionPoints();
        break;
      case TransformerBankType.deltaToWye:
        _generateDeltaWyeConnectionPoints();
        break;
      case TransformerBankType.openDelta:
        _generateOpenDeltaConnectionPoints();
        break;
    }
  }

  void _generateWyeWyeConnectionPoints() {
    // Primary side connection points (H1, H2 for each transformer)
    _connectionPoints.addAll([
      // Transformer 1
      ConnectionPoint(
        id: 'T1_H1',
        position: const Offset(100, 150),
        label: 'T1-H1',
        type: ConnectionType.primary,
        isInput: true,
      ),
      ConnectionPoint(
        id: 'T1_H2',
        position: const Offset(100, 200),
        label: 'T1-H2',
        type: ConnectionType.primary,
        isInput: true,
      ),
      // Transformer 2
      ConnectionPoint(
        id: 'T2_H1',
        position: const Offset(200, 150),
        label: 'T2-H1',
        type: ConnectionType.primary,
        isInput: true,
      ),
      ConnectionPoint(
        id: 'T2_H2',
        position: const Offset(200, 200),
        label: 'T2-H2',
        type: ConnectionType.primary,
        isInput: true,
      ),
      // Transformer 3
      ConnectionPoint(
        id: 'T3_H1',
        position: const Offset(300, 150),
        label: 'T3-H1',
        type: ConnectionType.primary,
        isInput: true,
      ),
      ConnectionPoint(
        id: 'T3_H2',
        position: const Offset(300, 200),
        label: 'T3-H2',
        type: ConnectionType.primary,
        isInput: true,
      ),
      // Secondary side connection points (X1, X2, X3 for each transformer)
      // Transformer 1 Secondary
      ConnectionPoint(
        id: 'T1_X1',
        position: const Offset(100, 300),
        label: 'T1-X1',
        type: ConnectionType.secondary,
        isInput: false,
      ),
      ConnectionPoint(
        id: 'T1_X2',
        position: const Offset(100, 350),
        label: 'T1-X2',
        type: ConnectionType.secondary,
        isInput: false,
      ),
      // Transformer 2 Secondary
      ConnectionPoint(
        id: 'T2_X1',
        position: const Offset(200, 300),
        label: 'T2-X1',
        type: ConnectionType.secondary,
        isInput: false,
      ),
      ConnectionPoint(
        id: 'T2_X2',
        position: const Offset(200, 350),
        label: 'T2-X2',
        type: ConnectionType.secondary,
        isInput: false,
      ),
      // Transformer 3 Secondary
      ConnectionPoint(
        id: 'T3_X1',
        position: const Offset(300, 300),
        label: 'T3-X1',
        type: ConnectionType.secondary,
        isInput: false,
      ),
      ConnectionPoint(
        id: 'T3_X2',
        position: const Offset(300, 350),
        label: 'T3-X2',
        type: ConnectionType.secondary,
        isInput: false,
      ),
      // Neutral points
      ConnectionPoint(
        id: 'PRIMARY_NEUTRAL',
        position: const Offset(200, 100),
        label: 'Primary Neutral',
        type: ConnectionType.neutral,
        isInput: true,
      ),
      ConnectionPoint(
        id: 'SECONDARY_NEUTRAL',
        position: const Offset(200, 400),
        label: 'Secondary Neutral',
        type: ConnectionType.neutral,
        isInput: false,
      ),
    ]);
  }

  void _generateDeltaDeltaConnectionPoints() {
    // Similar structure but for Delta-Delta configuration
    _connectionPoints.addAll([
      // Primary Delta connections
      ConnectionPoint(
        id: 'T1_H1',
        position: const Offset(100, 150),
        label: 'T1-H1',
        type: ConnectionType.primary,
        isInput: true,
      ),
      ConnectionPoint(
        id: 'T1_H2',
        position: const Offset(150, 120),
        label: 'T1-H2',
        type: ConnectionType.primary,
        isInput: true,
      ),
      // Add more connection points for Delta configuration...
    ]);
  }

  void _generateWyeDeltaConnectionPoints() {
    // Wye primary, Delta secondary
    // Implementation similar to above but with mixed configuration
  }

  void _generateDeltaWyeConnectionPoints() {
    // Delta primary, Wye secondary
    // Implementation similar to above but with mixed configuration
  }

  void _generateOpenDeltaConnectionPoints() {
    // Only two transformers for Open Delta
    _connectionPoints.addAll([
      // Transformer 1
      ConnectionPoint(
        id: 'T1_H1',
        position: const Offset(100, 150),
        label: 'T1-H1',
        type: ConnectionType.primary,
        isInput: true,
      ),
      ConnectionPoint(
        id: 'T1_H2',
        position: const Offset(100, 200),
        label: 'T1-H2',
        type: ConnectionType.primary,
        isInput: true,
      ),
      // Transformer 2
      ConnectionPoint(
        id: 'T2_H1',
        position: const Offset(250, 150),
        label: 'T2-H1',
        type: ConnectionType.primary,
        isInput: true,
      ),
      ConnectionPoint(
        id: 'T2_H2',
        position: const Offset(250, 200),
        label: 'T2-H2',
        type: ConnectionType.primary,
        isInput: true,
      ),
    ]);
  }

  @override
  void dispose() {
    _successAnimationController.dispose();
    _hintAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.offWhite,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // Top half - Transformer diagram
          Expanded(
            flex: 3,
            child: _buildTransformerDiagramSection(),
          ),
          // Bottom half - Interactive workbench
          Expanded(
            flex: 2,
            child: _buildWorkbenchSection(),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.primaryNavy,
      foregroundColor: AppTheme.white,
      title: Text(
        widget.isReferenceMode ? 'Reference Mode' : 'Training Mode',
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      actions: [
        if (!widget.isReferenceMode) ...[
          IconButton(
            icon: Icon(_showHints ? Icons.lightbulb : Icons.lightbulb_outline),
            onPressed: _toggleHints,
            tooltip: 'Toggle Hints',
          ),
        ],
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _resetWorkbench,
          tooltip: 'Reset',
        ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: _showOptionsMenu,
        ),
      ],
    );
  }

  Widget _buildTransformerDiagramSection() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        boxShadow: const [AppTheme.shadowSm],
        border: Border.all(color: AppTheme.borderLight),
      ),
      child: Stack(
        children: [
          // Background image
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
              child: Image.asset(
                'assets/images/blank-three-bank.png',
                fit: BoxFit.contain,
              ),
            ),
          ),
          // Connection points overlay
          Positioned.fill(
            child: CustomPaint(
              painter: WireConnectionPainter(
                connections: _connections,
                connectionPoints: _connectionPoints,
                wireColors: _wireColors,
                showConnections: widget.isReferenceMode,
              ),
            ),
          ),
          // Interactive connection points
          ..._buildConnectionPointWidgets(),
          // Validation feedback overlay
          if (_showValidation) _buildValidationOverlay(),
          // Hint overlay
          if (_showHints && _currentHint != null) _buildHintOverlay(),
        ],
      ),
    );
  }

  List<Widget> _buildConnectionPointWidgets() {
    return _connectionPoints.map((point) {
      final isSelected = _selectedConnectionPointId == point.id;
      final isConnected = _connections.any(
        (conn) => conn.fromPointId == point.id || conn.toPointId == point.id,
      );
      final isCompatible = _isCompatibleConnection(point.id);

      return Positioned(
        left: point.position.dx - 22, // Center the touch target
        top: point.position.dy - 22,
        child: ConnectionPointTooltip(
          connectionPoint: point,
          child: ConnectionPointWidget(
            connectionPoint: point,
            isSelected: isSelected,
            isConnected: isConnected,
            showGuidance: _showHints,
            isCompatible: isCompatible,
            isDragSource: isSelected,
            connectionMode: _connectionMode,
            onTap: () => _handleConnectionPointTap(point.id),
            onDragStart: () => _handleDragStart(point.id),
            onDragEnd: _handleDragEnd,
            onAcceptDrop: (details) => _handleConnectionDrop(details.data, point.id),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildWorkbenchSection() {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppTheme.radiusLg),
          topRight: Radius.circular(AppTheme.radiusLg),
        ),
        boxShadow: [AppTheme.shadowMd],
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: AppTheme.spacingSm),
            decoration: BoxDecoration(
              color: AppTheme.borderLight,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Workbench content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              child: Column(
                children: [
                  _buildToolsRow(),
                  const SizedBox(height: AppTheme.spacingMd),
                  _buildConfigurationOptions(),
                  const SizedBox(height: AppTheme.spacingMd),
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolsRow() {
    return Row(
      children: [
        // Connection mode toggle
        Expanded(
          child: _buildToolCard(
            title: 'Connection Mode',
            child: SegmentedButton<ConnectionMode>(
              segments: const [
                ButtonSegment(
                  value: ConnectionMode.stickyKeys,
                  label: Text('Tap'),
                  icon: Icon(Icons.touch_app, size: 16),
                ),
                ButtonSegment(
                  value: ConnectionMode.dragAndDrop,
                  label: Text('Drag'),
                  icon: Icon(Icons.drag_indicator, size: 16),
                ),
              ],
              selected: {_connectionMode},
              onSelectionChanged: (Set<ConnectionMode> selection) {
                setState(() {
                  _connectionMode = selection.first;
                });
              },
              style: SegmentedButton.styleFrom(
                selectedBackgroundColor: AppTheme.accentCopper.withOpacity(0.2),
                selectedForegroundColor: AppTheme.accentCopper,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConfigurationOptions() {
    return Row(
      children: [
        // Phase selection
        Expanded(
          child: _buildToolCard(
            title: 'Phase',
            child: Wrap(
              spacing: AppTheme.spacingSm,
              children: _wireColors.keys.map((phase) {
                final isSelected = _selectedPhase == phase;
                return FilterChip(
                  label: Text(phase),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedPhase = phase;
                      _selectedWireColor = _wireColors[phase]!;
                    });
                  },
                  selectedColor: _wireColors[phase]!.withOpacity(0.3),
                  checkmarkColor: _wireColors[phase],
                  side: BorderSide(color: _wireColors[phase]!),
                );
              }).toList(),
            ),
          ),
        ),
        const SizedBox(width: AppTheme.spacingMd),
        // Wire color preview
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: _selectedWireColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusSm),
            border: Border.all(color: AppTheme.borderLight, width: 2),
          ),
          child: const Icon(
            Icons.cable,
            color: Colors.white,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // Reset button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _resetWorkbench,
            icon: const Icon(Icons.refresh),
            label: const Text('Reset'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.borderLight,
              foregroundColor: AppTheme.textSecondary,
            ),
          ),
        ),
        const SizedBox(width: AppTheme.spacingMd),
        // Validation button
        if (!widget.isReferenceMode) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _validateConnections,
              icon: const Icon(Icons.check_circle),
              label: const Text('Check'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.accentCopper,
                foregroundColor: AppTheme.white,
              ),
            ),
          ),
          const SizedBox(width: AppTheme.spacingMd),
        ],
        // Help button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _showHelp,
            icon: const Icon(Icons.help_outline),
            label: const Text('Help'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.infoBlue,
              foregroundColor: AppTheme.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildToolCard({required String title, required Widget child}) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.lightGray,
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
        border: Border.all(color: AppTheme.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondary,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          child,
        ],
      ),
    );
  }

  Widget _buildValidationOverlay() {
    return AnimatedBuilder(
      animation: _successAnimation,
      builder: (context, child) {
        return Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1 * _successAnimation.value),
              borderRadius: BorderRadius.circular(AppTheme.radiusMd),
            ),
            child: Center(
              child: Transform.scale(
                scale: _successAnimation.value,
                child: Container(
                  padding: const EdgeInsets.all(AppTheme.spacingLg),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.white,
                        size: 48,
                      ),
                      const SizedBox(height: AppTheme.spacingSm),
                      Text(
                        _validationMessage ?? 'Correct!',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHintOverlay() {
    return AnimatedBuilder(
      animation: _hintAnimation,
      builder: (context, child) {
        return Positioned(
          top: 20,
          left: 20,
          right: 20,
          child: Transform.translate(
            offset: Offset(0, -50 * (1 - _hintAnimation.value)),
            child: Opacity(
              opacity: _hintAnimation.value,
              child: Container(
                padding: const EdgeInsets.all(AppTheme.spacingMd),
                decoration: BoxDecoration(
                  color: AppTheme.infoBlue,
                  borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                  boxShadow: const [AppTheme.shadowMd],
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.lightbulb,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: AppTheme.spacingMd),
                    Expanded(
                      child: Text(
                        _currentHint!,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _currentHint = null;
                        });
                        _hintAnimationController.reverse();
                      },
                      icon: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Event handlers
  void _handleConnectionPointTap(String pointId) {
    setState(() {
      if (_selectedConnectionPointId == null) {
        _selectedConnectionPointId = pointId;
        HapticFeedback.selectionClick();
      } else if (_selectedConnectionPointId == pointId) {
        _selectedConnectionPointId = null;
        HapticFeedback.lightImpact();
      } else {
        // Create connection
        _createConnection(_selectedConnectionPointId!, pointId);
        _selectedConnectionPointId = null;
      }
    });
  }

  void _handleDragStart(String pointId) {
    setState(() {
      _selectedConnectionPointId = pointId;
    });
  }

  void _handleDragEnd() {
    setState(() {
      _selectedConnectionPointId = null;
    });
  }

  void _handleConnectionDrop(String fromPointId, String toPointId) {
    _createConnection(fromPointId, toPointId);
  }

  void _createConnection(String fromPointId, String toPointId) {
    if (fromPointId == toPointId) return;

    // Check if connection already exists
    final existingConnection = _connections.any(
      (conn) => 
        (conn.fromPointId == fromPointId && conn.toPointId == toPointId) ||
        (conn.fromPointId == toPointId && conn.toPointId == fromPointId),
    );

    if (existingConnection) {
      _showErrorMessage('Connection already exists');
      return;
    }

    // Validate connection
    final isValid = _validateConnection(fromPointId, toPointId);
    
    setState(() {
      _connections.add(WireConnection(
        fromPointId: fromPointId,
        toPointId: toPointId,
        isCorrect: isValid,
        errorReason: isValid ? null : 'Invalid connection type',
      ));
    });

    if (isValid) {
      HapticFeedback.heavyImpact();
      _showSuccessMessage('Connection added successfully');
    } else {
      HapticFeedback.mediumImpact();
      _showErrorMessage('Invalid connection');
    }
  }

  bool _validateConnection(String fromPointId, String toPointId) {
    final fromPoint = _connectionPoints.firstWhere((p) => p.id == fromPointId);
    final toPoint = _connectionPoints.firstWhere((p) => p.id == toPointId);

    // Basic validation rules
    if (fromPoint.type == toPoint.type && fromPoint.isInput == toPoint.isInput) {
      return false; // Can't connect same type inputs/outputs
    }

    // Add more sophisticated validation based on transformer configuration
    return true;
  }

  bool _isCompatibleConnection(String pointId) {
    if (_selectedConnectionPointId == null) return false;
    if (_selectedConnectionPointId == pointId) return false;

    return _validateConnection(_selectedConnectionPointId!, pointId);
  }

  void _validateConnections() {
    // Implement validation logic based on transformer configuration
    final correctConnections = _connections.where((conn) => conn.isCorrect).length;
    final totalConnections = _connections.length;

    if (totalConnections == 0) {
      _showErrorMessage('No connections made yet');
      return;
    }

    final accuracy = correctConnections / totalConnections;
    
    if (accuracy >= 0.8) {
      _showValidationSuccess('Excellent work! ${(accuracy * 100).round()}% correct');
    } else if (accuracy >= 0.6) {
      _showValidationWarning('Good progress! ${(accuracy * 100).round()}% correct');
    } else {
      _showValidationError('Keep trying! ${(accuracy * 100).round()}% correct');
    }
  }

  void _showValidationSuccess(String message) {
    setState(() {
      _validationMessage = message;
      _showValidation = true;
    });
    _successAnimationController.forward().then((_) {
      Future.delayed(const Duration(seconds: 2), () {
        _successAnimationController.reverse();
        setState(() {
          _showValidation = false;
        });
      });
    });
  }

  void _showValidationWarning(String message) {
    _showSnackBar(message, Colors.orange);
  }

  void _showValidationError(String message) {
    _showSnackBar(message, Colors.red);
  }

  void _showSuccessMessage(String message) {
    _showSnackBar(message, Colors.green);
  }

  void _showErrorMessage(String message) {
    _showSnackBar(message, Colors.red);
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _toggleHints() {
    setState(() {
      _showHints = !_showHints;
      if (_showHints) {
        _currentHint = _getNextHint();
        _hintAnimationController.forward();
      } else {
        _hintAnimationController.reverse();
      }
    });
  }

  String _getNextHint() {
    // Provide contextual hints based on current state
    if (_connections.isEmpty) {
      return 'Start by connecting the primary side (H1, H2) terminals';
    } else if (_connections.length < 3) {
      return 'Connect the secondary side (X1, X2) terminals next';
    } else {
      return 'Don\'t forget to connect the neutral points for safety';
    }
  }

  void _resetWorkbench() {
    setState(() {
      _connections.clear();
      _selectedConnectionPointId = null;
      _showValidation = false;
      _currentHint = null;
    });
    HapticFeedback.mediumImpact();
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transformer Workbench Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Connection Modes:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• Tap Mode: Tap first point, then tap second point to connect'),
              Text('• Drag Mode: Long press and drag from one point to another'),
              SizedBox(height: 16),
              Text(
                'Connection Points:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• Red: Primary side (high voltage)'),
              Text('• Blue: Secondary side (low voltage)'),
              Text('• Gray: Neutral connections'),
              Text('• Green: Ground connections'),
              SizedBox(height: 16),
              Text(
                'Tips:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• Use hints for guidance'),
              Text('• Check your work with the validation button'),
              Text('• Reset to start over'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppTheme.spacingLg),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Settings'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to settings
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('About this Configuration'),
              onTap: () {
                Navigator.pop(context);
                _showConfigurationInfo();
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share Progress'),
              onTap: () {
                Navigator.pop(context);
                // Implement sharing
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showConfigurationInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${widget.bankType.name} Configuration'),
        content: Text(_getConfigurationDescription()),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _getConfigurationDescription() {
    switch (widget.bankType) {
      case TransformerBankType.wyeToWye:
        return 'Wye-Wye configuration provides a neutral point on both primary and secondary sides. Commonly used for 120V/208V systems.';
      case TransformerBankType.deltaToDelta:
        return 'Delta-Delta configuration provides maximum power transfer efficiency. No neutral point available.';
      case TransformerBankType.wyeToDelta:
        return 'Wye-Delta configuration steps down voltage while providing phase shift. Primary has neutral point.';
      case TransformerBankType.deltaToWye:
        return 'Delta-Wye configuration steps up voltage and provides neutral point on secondary side.';
      case TransformerBankType.openDelta:
        return 'Open Delta uses only two transformers to provide three-phase power at 86.6% capacity. Emergency configuration.';
    }
  }
}

/// Custom painter for drawing wire connections
class WireConnectionPainter extends CustomPainter {
  final List<WireConnection> connections;
  final List<ConnectionPoint> connectionPoints;
  final Map<String, Color> wireColors;
  final bool showConnections;

  WireConnectionPainter({
    required this.connections,
    required this.connectionPoints,
    required this.wireColors,
    required this.showConnections,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (!showConnections && connections.isEmpty) return;

    final paint = Paint()
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    for (final connection in connections) {
      final fromPoint = connectionPoints.firstWhere(
        (p) => p.id == connection.fromPointId,
      );
      final toPoint = connectionPoints.firstWhere(
        (p) => p.id == connection.toPointId,
      );

      // Set wire color based on connection validity
      paint.color = connection.isCorrect 
        ? Colors.green 
        : Colors.red.withOpacity(0.7);

      // Draw curved wire
      final path = Path();
      path.moveTo(fromPoint.position.dx, fromPoint.position.dy);
      
      // Create a curved path for more realistic wire appearance
      final controlPoint1 = Offset(
        fromPoint.position.dx + (toPoint.position.dx - fromPoint.position.dx) * 0.3,
        fromPoint.position.dy - 20,
      );
      final controlPoint2 = Offset(
        fromPoint.position.dx + (toPoint.position.dx - fromPoint.position.dx) * 0.7,
        toPoint.position.dy - 20,
      );
      
      path.cubicTo(
        controlPoint1.dx, controlPoint1.dy,
        controlPoint2.dx, controlPoint2.dy,
        toPoint.position.dx, toPoint.position.dy,
      );

      canvas.drawPath(path, paint);

      // Draw connection indicators at endpoints
      final indicatorPaint = Paint()
        ..color = paint.color
        ..style = PaintingStyle.fill;

      canvas.drawCircle(fromPoint.position, 4, indicatorPaint);
      canvas.drawCircle(toPoint.position, 4, indicatorPaint);
    }
  }

  @override
  bool shouldRepaint(covariant WireConnectionPainter oldDelegate) {
    return connections != oldDelegate.connections ||
           showConnections != oldDelegate.showConnections;
  }
}
