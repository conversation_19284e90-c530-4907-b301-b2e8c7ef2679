{"indexes": [{"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "local", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classification", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "typeOfWork", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "local", "order": "ASCENDING"}, {"fieldPath": "classification", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "typeOfWork", "order": "ASCENDING"}, {"fieldPath": "constructionType", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classification", "order": "ASCENDING"}, {"fieldPath": "constructionType", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "locals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "local_union", "order": "ASCENDING"}]}, {"collectionGroup": "locals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "state", "order": "ASCENDING"}, {"fieldPath": "local_union", "order": "ASCENDING"}]}, {"collectionGroup": "locals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "state", "order": "ASCENDING"}, {"fieldPath": "city", "order": "ASCENDING"}]}], "fieldOverrides": []}