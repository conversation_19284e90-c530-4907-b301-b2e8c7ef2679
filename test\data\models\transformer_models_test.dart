import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../lib/electrical_components/transformer_trainer/models/transformer_models.dart';

void main() {
  group('ConnectionPoint Tests', () {
    test('creates connection point with all properties', () {
      const connectionPoint = ConnectionPoint(
        id: 'H1',
        label: 'H1 Primary',
        type: ConnectionType.primary,
        position: Offset(100, 150),
        isInput: true,
      );

      expect(connectionPoint.id, equals('H1'));
      expect(connectionPoint.label, equals('H1 Primary'));
      expect(connectionPoint.type, equals(ConnectionType.primary));
      expect(connectionPoint.position, equals(const Offset(100, 150)));
      expect(connectionPoint.isInput, isTrue);
    });

    test('connection points with same id are equal', () {
      const connectionPoint1 = ConnectionPoint(
        id: 'H1',
        label: 'H1 Primary',
        type: ConnectionType.primary,
        position: Offset(100, 150),
        isInput: true,
      );

      const connectionPoint2 = ConnectionPoint(
        id: 'H1',
        label: 'H1 Primary Alt',
        type: ConnectionType.secondary,
        position: Offset(200, 250),
        isInput: false,
      );

      expect(connectionPoint1, equals(connectionPoint2));
      expect(connectionPoint1.hashCode, equals(connectionPoint2.hashCode));
    });

    test('different connection points are not equal', () {
      const connectionPoint1 = ConnectionPoint(
        id: 'H1',
        label: 'H1 Primary',
        type: ConnectionType.primary,
        position: Offset(100, 150),
        isInput: true,
      );

      const connectionPoint2 = ConnectionPoint(
        id: 'H2',
        label: 'H2 Primary',
        type: ConnectionType.primary,
        position: Offset(100, 150),
        isInput: true,
      );

      expect(connectionPoint1, isNot(equals(connectionPoint2)));
    });
  });

  group('WireConnection Tests', () {
    late ConnectionPoint fromPoint;
    late ConnectionPoint toPoint;

    setUp(() {
      fromPoint = const ConnectionPoint(
        id: 'H1',
        label: 'H1',
        type: ConnectionType.primary,
        position: Offset(100, 100),
        isInput: true,
      );

      toPoint = const ConnectionPoint(
        id: 'X1',
        label: 'X1',
        type: ConnectionType.secondary,
        position: Offset(200, 200),
        isInput: false,
      );
    });

    test('creates wire connection correctly', () {
      final connection = WireConnection(
        from: fromPoint,
        to: toPoint,
        isCorrect: true,
        phaseShift: 30,
      );

      expect(connection.from, equals(fromPoint));
      expect(connection.to, equals(toPoint));
      expect(connection.isCorrect, isTrue);
      expect(connection.phaseShift, equals(30));
    });

    test('validates correct connections', () {
      final correctConnection = WireConnection(
        from: fromPoint,
        to: toPoint,
        isCorrect: true,
      );

      expect(correctConnection.isCorrect, isTrue);
    });

    test('identifies incorrect connections', () {
      final incorrectConnection = WireConnection(
        from: fromPoint,
        to: toPoint,
        isCorrect: false,
      );

      expect(incorrectConnection.isCorrect, isFalse);
    });

    test('handles optional phase shift', () {
      final connectionWithPhase = WireConnection(
        from: fromPoint,
        to: toPoint,
        phaseShift: 30,
      );

      final connectionWithoutPhase = WireConnection(
        from: fromPoint,
        to: toPoint,
      );

      expect(connectionWithPhase.phaseShift, equals(30));
      expect(connectionWithoutPhase.phaseShift, isNull);
    });
  });

  group('TrainingStep Tests', () {
    test('creates training step with all properties', () {
      const step = TrainingStep(
        id: 'step_1',
        instruction: 'Connect H1 to X1',
        expectedConnections: ['H1-X1'],
        hints: ['Look for the primary winding'],
        commonMistakes: ['Do not connect H1 to X2'],
      );

      expect(step.id, equals('step_1'));
      expect(step.instruction, equals('Connect H1 to X1'));
      expect(step.expectedConnections, equals(['H1-X1']));
      expect(step.hints, equals(['Look for the primary winding']));
      expect(step.commonMistakes, equals(['Do not connect H1 to X2']));
    });

    test('handles empty collections', () {
      const step = TrainingStep(
        id: 'step_empty',
        instruction: 'Basic step',
        expectedConnections: [],
        hints: [],
        commonMistakes: [],
      );

      expect(step.expectedConnections, isEmpty);
      expect(step.hints, isEmpty);
      expect(step.commonMistakes, isEmpty);
    });
  });

  group('EducationalContent Tests', () {
    test('creates educational content correctly', () {
      const content = EducationalContent(
        title: 'Wye-Delta Transformation',
        description: 'Learn about phase relationships',
        safetyNotes: ['Always de-energize before connecting'],
        realWorldApplications: ['Industrial motor starting'],
        commonMistakes: ['Incorrect phasing'],
        difficultyLevel: DifficultyLevel.intermediate,
      );

      expect(content.title, equals('Wye-Delta Transformation'));
      expect(content.description, equals('Learn about phase relationships'));
      expect(content.safetyNotes, equals(['Always de-energize before connecting']));
      expect(content.realWorldApplications, equals(['Industrial motor starting']));
      expect(content.commonMistakes, equals(['Incorrect phasing']));
      expect(content.difficultyLevel, equals(DifficultyLevel.intermediate));
    });
  });

  group('TrainingState Tests', () {
    test('creates training state with defaults', () {
      const state = TrainingState();

      expect(state.bankType, equals(TransformerBankType.wyeToWye));
      expect(state.mode, equals(TrainingMode.guided));
      expect(state.difficulty, equals(DifficultyLevel.beginner));
      expect(state.currentStep, equals(0));
      expect(state.connections, isEmpty);
      expect(state.isComplete, isFalse);
      expect(state.errors, isEmpty);
    });

    test('creates training state with custom values', () {
      final connections = [
        WireConnection(
          from: const ConnectionPoint(
            id: 'H1',
            label: 'H1',
            type: ConnectionType.primary,
            position: Offset(0, 0),
            isInput: true,
          ),
          to: const ConnectionPoint(
            id: 'X1',
            label: 'X1',
            type: ConnectionType.secondary,
            position: Offset(100, 100),
            isInput: false,
          ),
        ),
      ];

      final state = TrainingState(
        bankType: TransformerBankType.deltaToWye,
        mode: TrainingMode.quiz,
        difficulty: DifficultyLevel.advanced,
        currentStep: 5,
        connections: connections,
        isComplete: true,
        errors: ['Connection error'],
      );

      expect(state.bankType, equals(TransformerBankType.deltaToWye));
      expect(state.mode, equals(TrainingMode.quiz));
      expect(state.difficulty, equals(DifficultyLevel.advanced));
      expect(state.currentStep, equals(5));
      expect(state.connections, equals(connections));
      expect(state.isComplete, isTrue);
      expect(state.errors, equals(['Connection error']));
    });

    test('copyWith creates new instance with updated values', () {
      const originalState = TrainingState();

      final updatedState = originalState.copyWith(
        bankType: TransformerBankType.deltaToWye,
        currentStep: 3,
        isComplete: true,
      );

      // Original state unchanged
      expect(originalState.bankType, equals(TransformerBankType.wyeToWye));
      expect(originalState.currentStep, equals(0));
      expect(originalState.isComplete, isFalse);

      // Updated state has new values
      expect(updatedState.bankType, equals(TransformerBankType.deltaToWye));
      expect(updatedState.currentStep, equals(3));
      expect(updatedState.isComplete, isTrue);

      // Unchanged values are preserved
      expect(updatedState.mode, equals(TrainingMode.guided));
      expect(updatedState.difficulty, equals(DifficultyLevel.beginner));
    });
  });

  group('Enum Tests', () {
    test('TransformerBankType enum has all expected values', () {
      expect(TransformerBankType.values, hasLength(5));
      expect(TransformerBankType.values, contains(TransformerBankType.wyeToWye));
      expect(TransformerBankType.values, contains(TransformerBankType.wyeToDelta));
      expect(TransformerBankType.values, contains(TransformerBankType.deltaToWye));
      expect(TransformerBankType.values, contains(TransformerBankType.deltaToDelta));
      expect(TransformerBankType.values, contains(TransformerBankType.openDelta));
    });

    test('ConnectionType enum has all expected values', () {
      expect(ConnectionType.values, hasLength(4));
      expect(ConnectionType.values, contains(ConnectionType.primary));
      expect(ConnectionType.values, contains(ConnectionType.secondary));
      expect(ConnectionType.values, contains(ConnectionType.neutral));
      expect(ConnectionType.values, contains(ConnectionType.ground));
    });

    test('TrainingMode enum has all expected values', () {
      expect(TrainingMode.values, hasLength(2));
      expect(TrainingMode.values, contains(TrainingMode.guided));
      expect(TrainingMode.values, contains(TrainingMode.quiz));
    });

    test('DifficultyLevel enum has all expected values', () {
      expect(DifficultyLevel.values, hasLength(3));
      expect(DifficultyLevel.values, contains(DifficultyLevel.beginner));
      expect(DifficultyLevel.values, contains(DifficultyLevel.intermediate));
      expect(DifficultyLevel.values, contains(DifficultyLevel.advanced));
    });

    test('ConnectionMode enum has all expected values', () {
      expect(ConnectionMode.values, hasLength(2));
      expect(ConnectionMode.values, contains(ConnectionMode.stickyKeys));
      expect(ConnectionMode.values, contains(ConnectionMode.dragAndDrop));
    });
  });

  group('Integration Tests', () {
    test('can create complete training scenario', () {
      // Create connection points
      const h1 = ConnectionPoint(
        id: 'H1',
        label: 'H1 Primary',
        type: ConnectionType.primary,
        position: Offset(50, 100),
        isInput: true,
      );

      const x1 = ConnectionPoint(
        id: 'X1',
        label: 'X1 Secondary',
        type: ConnectionType.secondary,
        position: Offset(250, 100),
        isInput: false,
      );

      // Create connection
      final connection = WireConnection(
        from: h1,
        to: x1,
        isCorrect: true,
        phaseShift: 0,
      );

      // Create training step
      const step = TrainingStep(
        id: 'wye_step_1',
        instruction: 'Connect H1 to X1 for in-phase relationship',
        expectedConnections: ['H1-X1'],
        hints: ['Primary H1 connects to secondary X1'],
        commonMistakes: ['Do not connect H1 to X2'],
      );

      // Create educational content
      const content = EducationalContent(
        title: 'Wye-Wye Transformer Bank',
        description: 'Learn the basics of wye-wye connections',
        safetyNotes: ['Ensure proper grounding'],
        realWorldApplications: ['Distribution transformers'],
        commonMistakes: ['Incorrect neutral connections'],
        difficultyLevel: DifficultyLevel.beginner,
      );

      // Create training state
      final state = TrainingState(
        bankType: TransformerBankType.wyeToWye,
        mode: TrainingMode.guided,
        difficulty: DifficultyLevel.beginner,
        currentStep: 0,
        connections: [connection],
        isComplete: false,
        errors: [],
      );

      // Verify all components work together
      expect(state.connections, contains(connection));
      expect(connection.from, equals(h1));
      expect(connection.to, equals(x1));
      expect(step.expectedConnections, contains('H1-X1'));
      expect(content.difficultyLevel, equals(state.difficulty));
    });
  });
}