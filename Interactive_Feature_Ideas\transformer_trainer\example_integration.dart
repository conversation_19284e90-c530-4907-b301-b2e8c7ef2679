
// Example showing how to integrate the TransformerTrainer widget
// into the existing Journeyman Jobs app

import 'package:flutter/material.dart';
import 'package:transformer_trainer/transformer_trainer.dart';

class TransformerTrainingScreen extends StatefulWidget {
  @override
  _TransformerTrainingScreenState createState() => _TransformerTrainingScreenState();
}

class _TransformerTrainingScreenState extends State<TransformerTrainingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Transformer Bank Training'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: TransformerTrainer(
        initialBankType: TransformerBankType.wyeToWye,
        initialMode: TrainingMode.guided,
        initialDifficulty: DifficultyLevel.beginner,
        
        // Callbacks for integration with host app
        onStepComplete: (step) {
          print('Step ${step.stepNumber} completed: ${step.instruction}');
          // Could trigger analytics, progress tracking, etc.
        },
        
        onBankComplete: (bankType) {
          print('Bank complete: $bankType');
          // Could unlock next lesson, award points, etc.
          _showCompletionDialog(bankType);
        },
        
        onError: (error) {
          print('Training error: $error');
          // Could show help hints, increment mistake counter, etc.
        },
      ),
    );
  }

  void _showCompletionDialog(TransformerBankType bankType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Training Complete!'),
        content: Text(
          'You have successfully completed the ${_getBankTypeName(bankType)} training module.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Continue'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to next training module or return to course menu
            },
            child: Text('Next Module'),
          ),
        ],
      ),
    );
  }

  String _getBankTypeName(TransformerBankType type) {
    switch (type) {
      case TransformerBankType.wyeToWye:
        return 'Wye-Wye';
      case TransformerBankType.deltaToDelta:
        return 'Delta-Delta';
      case TransformerBankType.wyeToDelta:
        return 'Wye-Delta';
      case TransformerBankType.deltaToWye:
        return 'Delta-Wye';
      case TransformerBankType.openDelta:
        return 'Open Delta';
    }
  }
}

// Alternative minimal integration
class MinimalTransformerTrainer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return TransformerTrainer(
      // Use all defaults - very simple integration
      onBankComplete: (bankType) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Training completed!')),
        );
      },
    );
  }
}

// Advanced integration with custom UI
class CustomTransformerTrainingPage extends StatefulWidget {
  @override
  _CustomTransformerTrainingPageState createState() => _CustomTransformerTrainingPageState();
}

class _CustomTransformerTrainingPageState extends State<CustomTransformerTrainingPage> {
  TransformerBankType currentBankType = TransformerBankType.wyeToWye;
  TrainingMode currentMode = TrainingMode.guided;
  int completedSteps = 0;
  List<String> completedBanks = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Advanced Transformer Training'),
        actions: [
          PopupMenuButton<TransformerBankType>(
            onSelected: (type) => setState(() => currentBankType = type),
            itemBuilder: (context) => TransformerBankType.values.map(
              (type) => PopupMenuItem(
                value: type,
                child: Text(_getBankTypeName(type)),
              ),
            ).toList(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Custom progress indicator
          Container(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                Text('Completed Banks: ${completedBanks.length}/5'),
                LinearProgressIndicator(
                  value: completedBanks.length / 5.0,
                ),
              ],
            ),
          ),
          
          // Mode toggle
          Row(
            children: [
              Expanded(
                child: RadioListTile<TrainingMode>(
                  title: Text('Guided'),
                  value: TrainingMode.guided,
                  groupValue: currentMode,
                  onChanged: (mode) => setState(() => currentMode = mode!),
                ),
              ),
              Expanded(
                child: RadioListTile<TrainingMode>(
                  title: Text('Quiz'),
                  value: TrainingMode.quiz,
                  groupValue: currentMode,
                  onChanged: (mode) => setState(() => currentMode = mode!),
                ),
              ),
            ],
          ),
          
          // Main trainer widget
          Expanded(
            child: TransformerTrainer(
              key: ValueKey('${currentBankType}_${currentMode}'), // Force rebuild on changes
              initialBankType: currentBankType,
              initialMode: currentMode,
              onStepComplete: (step) {
                setState(() => completedSteps++);
              },
              onBankComplete: (bankType) {
                if (!completedBanks.contains(_getBankTypeName(bankType))) {
                  setState(() {
                    completedBanks.add(_getBankTypeName(bankType));
                  });
                }
                
                if (completedBanks.length == 5) {
                  _showCourseCompletionDialog();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showCourseCompletionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('🎉 Course Complete!'),
        content: Text(
          'Congratulations! You have completed all transformer bank configurations. '
          'You are now ready for field application of transformer bank connections.',
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Return to course selection or dashboard
            },
            child: Text('Finish Course'),
          ),
        ],
      ),
    );
  }

  String _getBankTypeName(TransformerBankType type) {
    switch (type) {
      case TransformerBankType.wyeToWye:
        return 'Wye-Wye';
      case TransformerBankType.deltaToDelta:
        return 'Delta-Delta';
      case TransformerBankType.wyeToDelta:
        return 'Wye-Delta';
      case TransformerBankType.deltaToWye:
        return 'Delta-Wye';
      case TransformerBankType.openDelta:
        return 'Open Delta';
    }
  }
}
