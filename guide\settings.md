# Settings Screen Corrections

`This document outlines the corrections that need to be made to the Settings Screen.`

## *Settings Screen*

* In the first card underneath the users name I need to change IBEW member to that users ticket number.
* Need to add the copper border around each of the cards.

## *Account*

* Some where we need to add the ability to change the user's picture icon avatar so that they can upload a photo or choose a photo from their gallery
* We need to implement the safe space for the bottom of the screen because when I'm in edit mode half of the save change button is covered up by my Android navigation bar This system navigation bar.
* The save changes button needs to be stationary and stay visible above the Android navigation bar in edit mode

### Profile Screen

#### Personal Tab

* When you hit edit on the personal tab there is a right overflow by 4.9 pixels for the state drop down menu

#### Professional Tab

* This is the UI enhancement but possibly enhancing the check boxes to custom check boxes. Maybe something to do with chicken voltage or reclosure or something.

#### Settings Tab

* OK so the entire section for notification preferences need to be moved to the notification screen.
* We need to add you know some type of system settings Change to dark mode something else we need to change the settings we need to do something different with the settings.

### Training & Certificates Screen

#### Certifications

#### Courses

#### History

## *Support*

### Help Support Screen

#### FAQ

* Under thewhat does books on mean in my profile that description needs to be changed to "Books on" refers to what books are you on what out of work books have you already signed in you're actively on SO that we know what locals and what jobs to present to you ahead of others so that we're not showing you jobs that you aren't able to bid for.

### Resources Screen

### Send Feedback

## *App*

### Notifications

#### Notification Settings

### Privacy & Security

### About
