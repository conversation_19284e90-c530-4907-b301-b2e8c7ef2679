# IBEW Domain Knowledge

*Comprehensive guide to electrical worker classifications, union structure, and industry-specific requirements*

## ⚡ Overview

The International Brotherhood of Electrical Workers (IBEW) is the premier electrical union representing over 775,000 electrical workers across North America. Understanding IBEW structure, classifications, and terminology is crucial for developing effective features in the Journeyman Jobs application.

## 🏢 IBEW Organizational Structure

### Local Union System

The IBEW operates through **797+ local unions** across the United States and Canada, each serving specific geographic regions and classifications.

#### Local Union Structure

``` tree
IBEW International Office (Washington, D.C.)
├── International Districts (11 districts)
│   ├── District 1: New England & Eastern Canada
│   ├── District 2: New York & Northern New Jersey  
│   ├── District 3: Mid-Atlantic States
│   ├── District 4: Southeast & Caribbean
│   ├── District 5: Great Lakes Region
│   ├── District 6: South Central States
│   ├── District 7: Mountain States
│   ├── District 8: Pacific Coast
│   ├── District 9: Midwest Plains
│   ├── District 10: Western Canada
│   └── District 11: Pacific Northwest
└── Local Unions (797+ locals)
    ├── Inside Construction Locals
    ├── Outside Construction Locals  
    ├── Utility Locals
    ├── Manufacturing Locals
    └── Mixed/Combined Locals
```

#### Local Union Examples

- **IBEW Local 1**: St. Louis, MO (Inside Construction)
- **IBEW Local 3**: New York, NY (Inside Construction)
- **IBEW Local 77**: Seattle, WA (Inside Construction)
- **IBEW Local 125**: Portland, OR (Outside Construction)
- **IBEW Local 1245**: Oakland, CA (Utility)

### Jurisdictional Coverage

Each local union has specific geographic and work jurisdiction:

```dart
class IBEWLocal {
  final int localNumber;
  final String name;
  final String city;
  final String state;
  final List<Classification> classifications;
  final LocalType type;
  final List<String> counties;        // Geographic jurisdiction
  final List<String> contractors;     // Signatory contractors
  final ReferralRules referralRules;  // Local-specific referral procedures
}

enum LocalType {
  insideConstruction,    // Commercial/industrial electrical
  outsideConstruction,   // Transmission/distribution lines
  utility,              // Electric utility companies
  manufacturing,        // Electrical equipment manufacturing
  government,           // Government electrical work
  telecom,              // Telecommunications
  mixed,                // Multiple classifications
}
```

## 👷 Electrical Worker Classifications

### Primary Classifications

#### Inside Wireman (IW)

**Work Focus**: Commercial and industrial electrical installations
**Typical Duties**:

- Commercial building electrical systems
- Industrial control systems and motor control centers
- Fire alarm and security systems
- Data/telecommunications wiring
- Electrical troubleshooting and maintenance

**Voltage Levels**: Typically 600V and below
**Work Environments**:

- Office buildings and retail spaces
- Manufacturing facilities
- Hospitals and schools
- Data centers

```dart
enum InsideWorkTypes {
  commercial,           // Office buildings, retail
  industrial,           // Manufacturing, processing
  institutional,        // Schools, hospitals, government
  dataCenter,          // Server farms, telecommunications
  maintenance,         // Ongoing electrical maintenance
  renovation,          // Upgrade and modernization
}
```

#### Journeyman Lineman (JL)

**Work Focus**: Electrical transmission and distribution systems
**Typical Duties**:

- High-voltage power line construction and maintenance
- Substation construction and maintenance
- Storm restoration work
- Underground cable installation
- Live-line maintenance (energized systems)

**Voltage Levels**: 4kV to 765kV transmission systems
**Work Environments**:

- Overhead power lines
- Electrical substations
- Underground utility systems
- Emergency storm response

```dart
enum LinemanWorkTypes {
  transmission,         // High-voltage long-distance lines
  distribution,         // Local power distribution
  substation,          // Electrical substations
  underground,         // Underground cable systems
  stormRestoration,    // Emergency repair work
  maintenance,         // Routine system maintenance
  construction,        // New system construction
}
```

#### Tree Trimmer (TT)

**Work Focus**: Vegetation management around electrical systems
**Typical Duties**:

- Tree removal near power lines
- Vegetation management for electrical clearance
- Storm damage cleanup
- Right-of-way maintenance
- Herbicide application

**Safety Requirements**:

- Electrical hazard awareness
- Fall protection certification
- Chainsaw operation certification

#### Equipment Operator (EO)

**Work Focus**: Heavy machinery operation for electrical construction
**Typical Duties**:

- Operating cranes for line work
- Excavation for underground systems
- Material handling and transport
- Specialized electrical construction equipment

**Equipment Types**:

- Bucket trucks and aerial lifts
- Digger derricks
- Cable pulling equipment
- Trenching machines

### Specialized Classifications

#### Low Voltage Technician (LV)

**Work Focus**: Systems under 50 volts

- Fire alarm systems
- Security and access control
- Nurse call systems
- Audio/visual systems
- Building automation

#### Residential Wireman (RW)

**Work Focus**: Single and multi-family residential electrical

- House wiring and service installation
- Residential electrical troubleshooting
- Home automation systems
- Solar panel installation

#### Sound & Communications (S&C)

**Work Focus**: Audio, video, and data systems

- Telecommunications infrastructure
- Audio/visual system installation
- Fiber optic cable installation
- Network infrastructure

## 📋 Apprenticeship System

### Apprenticeship Structure

The IBEW operates comprehensive apprenticeship programs:

```dart
class ApprenticeshipProgram {
  final Classification classification;
  final int durationYears;              // Typically 4-5 years
  final int classroomHours;             // Required classroom instruction
  final int onJobHours;                 // On-the-job training hours
  final List<String> competencies;      // Required skills and knowledge
  final List<ExamRequirement> exams;    // Tests and certifications
}

enum ApprenticeshipLevel {
  firstYear,      // 0-1000 hours
  secondYear,     // 1000-2000 hours  
  thirdYear,      // 2000-3000 hours
  fourthYear,     // 3000-4000 hours
  fifthYear,      // 4000+ hours (some classifications)
}
```

### Progression Path

1. **Pre-Apprentice**: Basic electrical education
2. **Apprentice**: 4-5 year structured training program
3. **Journeyman**: Certified electrical worker
4. **Foreman**: Crew leadership role
5. **General Foreman**: Multi-crew supervision

## 📊 Job Referral System

### Referral Hall Operations

IBEW locals operate referral halls that manage job assignments:

#### Book System

```dart
class ReferralBook {
  final int bookNumber;               // Book 1, Book 2, etc.
  final String description;           // Book description
  final List<String> eligibility;     // Who can sign this book
  final int priority;                 // Referral priority (1 = highest)
}

// Standard IBEW referral books
final referralBooks = [
  ReferralBook(
    bookNumber: 1,
    description: "Unemployment - Local Members",
    eligibility: ["Local members who are unemployed"],
    priority: 1,
  ),
  ReferralBook(
    bookNumber: 2, 
    description: "Unemployment - Travelers",
    eligibility: ["IBEW members from other locals"],
    priority: 2,
  ),
  ReferralBook(
    bookNumber: 3,
    description: "Employment - Requesting Additional Work", 
    eligibility: ["Currently employed members seeking additional work"],
    priority: 3,
  ),
  ReferralBook(
    bookNumber: 4,
    description: "Apprentices and CE/CW",
    eligibility: ["Apprentices, Construction Electricians, Construction Wiremen"],
    priority: 4,
  ),
];
```

#### Referral Rules

Each local has specific referral procedures:

```dart
class ReferralRules {
  final Duration signInWindow;        // When members can sign books
  final Duration jobCallWindow;       // When jobs are called/filled  
  final bool allowPhoneReferrals;     // Phone vs. in-person only
  final Map<int, Duration> bookRotation; // How long before re-signing
  final List<String> specialRules;   // Local-specific procedures
}
```

### Travel Work Policies

#### Reciprocal Agreements

IBEW members can work in other local jurisdictions under reciprocal agreements:

```dart
class TravelWorkPolicy {
  final Duration localResidencyPeriod;    // Time before becoming local member
  final bool requiresWorkPermit;          // Work permit required
  final List<String> restrictedLocals;    // Locals with travel restrictions
  final Map<String, Duration> waitingPeriods; // Waiting periods by local
}
```

## 🏗️ Construction Types and Work Categories

### Construction Classifications

#### Commercial Construction

- **Office Buildings**: Corporate headquarters, business centers
- **Retail**: Shopping centers, stores, restaurants
- **Hospitality**: Hotels, entertainment venues
- **Healthcare**: Hospitals, clinics, medical facilities

#### Industrial Construction  

- **Manufacturing**: Factories, processing plants
- **Chemical/Petrochemical**: Refineries, chemical plants
- **Power Generation**: Power plants, renewable energy
- **Heavy Industry**: Steel mills, automotive manufacturing

#### Utility Work

- **Transmission**: High-voltage long-distance power lines
- **Distribution**: Local power delivery systems
- **Substations**: Electrical switching and transformation
- **Smart Grid**: Advanced electrical grid technology

#### Specialty Work

- **Storm Work**: Emergency restoration after natural disasters
- **Maintenance**: Ongoing electrical system upkeep
- **Renewable Energy**: Solar, wind, and battery installations
- **Data Centers**: High-reliability electrical systems

## 💰 Compensation and Benefits

### Wage Structure

IBEW wages are typically structured with multiple components:

```dart
class IBEWCompensation {
  final double baseWage;              // Base hourly rate
  final double fringe;                // Fringe benefits hourly amount
  final double pension;               // Pension contribution
  final double annuity;               // Annuity/401k contribution
  final double trainingFund;          // Training fund contribution
  final double total;                 // Total compensation package
  
  // Additional compensation
  final double? perDiem;              // Daily expense allowance
  final double? subsistence;          // Living expense allowance
  final bool mileageReimbursement;    // Travel expense coverage
}
```

### Wage Progression

```dart
enum WageLevel {
  apprenticeYear1,    // ~45% of journeyman rate
  apprenticeYear2,    // ~55% of journeyman rate
  apprenticeYear3,    // ~65% of journeyman rate
  apprenticeYear4,    // ~75% of journeyman rate
  apprenticeYear5,    // ~85% of journeyman rate (if applicable)
  journeyman,         // 100% rate
  foreman,           // 110-115% of journeyman rate
  generalForeman,    // 120-125% of journeyman rate
}
```

### Geographic Wage Variations

Wages vary significantly by geographic region:

```dart
class RegionalWageData {
  final String region;
  final Classification classification;
  final double averageWage;
  final double lowWage;
  final double highWage;
  final bool requiresPerDiem;
  final double typicalPerDiem;
}

// Example regional data
final regionalWages = [
  RegionalWageData(
    region: "San Francisco Bay Area",
    classification: Classification.insideWireman,
    averageWage: 65.00,
    lowWage: 58.00,
    highWage: 72.00,
    requiresPerDiem: true,
    typicalPerDiem: 100.00,
  ),
  RegionalWageData(
    region: "Rural Midwest",
    classification: Classification.insideWireman,
    averageWage: 35.00,
    lowWage: 28.00,
    highWage: 42.00,
    requiresPerDiem: false,
    typicalPerDiem: 0.00,
  ),
];
```

## 🛡️ Safety and Certifications

### Required Certifications

#### OSHA Training

- **OSHA 10**: Basic construction safety
- **OSHA 30**: Advanced construction safety (supervisors)

#### Electrical Safety

- **Arc Flash Training**: Protection from electrical arc hazards
- **Lockout/Tagout (LOTO)**: Electrical energy control procedures
- **Confined Space**: Work in enclosed spaces
- **Fall Protection**: Protection from falls

#### Specialized Certifications

```dart
class ElectricalCertification {
  final String name;
  final String issuingBody;
  final Duration validityPeriod;
  final bool requiresRenewal;
  final List<Classification> applicableClassifications;
}

final commonCertifications = [
  ElectricalCertification(
    name: "CPR/First Aid",
    issuingBody: "American Red Cross",
    validityPeriod: Duration(days: 730), // 2 years
    requiresRenewal: true,
    applicableClassifications: [Classification.all],
  ),
  ElectricalCertification(
    name: "Commercial Driver's License (CDL)",
    issuingBody: "State DMV",
    validityPeriod: Duration(days: 1825), // 5 years
    requiresRenewal: true,
    applicableClassifications: [
      Classification.journeymanLineman,
      Classification.equipmentOperator,
    ],
  ),
];
```

## 🌩️ Storm Work and Emergency Response

### Storm Work Classification

Storm work represents emergency electrical restoration following natural disasters:

#### Storm Work Characteristics

- **High Priority**: Immediate response required
- **Enhanced Pay**: Premium wages and overtime opportunities  
- **Travel Required**: Often requires out-of-state travel
- **Extended Hours**: 12-16 hour days, 7 days per week
- **Temporary Duration**: Typically 2-8 weeks

#### Storm Work Types

```dart
enum StormWorkType {
  hurricaneRestoration,     // Hurricane damage repair
  tornadoRestoration,      // Tornado damage repair
  iceStormRestoration,     // Ice storm damage repair
  wildFireRestoration,     // Fire damage repair
  floodRestoration,        // Flood damage repair
  windStormRestoration,    // High wind damage repair
}
```

#### Mutual Aid Agreements

IBEW locals have mutual aid agreements for storm response:

```dart
class MutualAidAgreement {
  final List<int> participatingLocals;
  final List<StormWorkType> coveredEvents;
  final CompensationStructure stormWages;
  final Duration deploymentPeriod;
  final List<String> equipmentRequirements;
}
```

## 🔧 Technical Specifications

### Voltage Classifications

Understanding voltage levels is crucial for job classification:

```dart
enum VoltageClassification {
  lowVoltage,        // Under 1000V (residential/commercial)
  mediumVoltage,     // 1kV to 35kV (distribution)
  highVoltage,       // 35kV to 138kV (sub-transmission)
  extraHighVoltage,  // 138kV to 800kV (transmission)
  ultraHighVoltage,  // Over 800kV (bulk transmission)
}

class VoltageLevel {
  final VoltageClassification classification;
  final double nominalVoltage;
  final List<Classification> qualifiedWorkers;
  final List<String> safetyRequirements;
}
```

### Electrical Systems Knowledge

#### Three-Phase Power Systems

Essential knowledge for electrical workers:

```dart
class ThreePhaseSystem {
  final SystemConfiguration configuration;
  final double lineVoltage;
  final double phaseVoltage;  
  final double frequency;     // 60Hz in North America
  final List<String> applications;
}

enum SystemConfiguration {
  wyeGrounded,        // Y-grounded (most common)
  wyeUngrounded,      // Y-ungrounded
  deltaGrounded,      // Δ-grounded
  deltaUngrounded,    // Δ-ungrounded
  openDelta,          // Open Δ (two transformers)
}
```

### Equipment and Tools

#### Standard Electrical Tools

```dart
class ElectricalTool {
  final String name;
  final ToolCategory category;
  final List<Classification> usedBy;
  final bool requiresCertification;
  final List<String> safetyRequirements;
}

enum ToolCategory {
  handTools,          // Pliers, screwdrivers, etc.
  powerTools,         // Drills, saws, etc.
  testEquipment,      // Multimeters, oscilloscopes
  safetyEquipment,    // Hard hats, safety glasses
  specializedTools,   // Cable pullers, benders
}
```

## 📱 Application Integration Considerations

### User Interface Adaptations

#### Terminology Usage

Use authentic IBEW terminology throughout the application:

```dart
class IBEWTerminology {
  // Use "Local" not "Union" 
  static const String localUnion = "Local";
  
  // Use "Book" not "List"
  static const String referralBook = "Book";
  
  // Use "Ticket Number" not "Member ID"
  static const String memberIdentifier = "Ticket Number";
  
  // Use "Classification" not "Trade"
  static const String workerType = "Classification";
}
```

#### Cultural Considerations

- **Brotherhood/Sisterhood**: Emphasize union solidarity
- **Mutual Aid**: Highlight member support systems
- **Safety First**: Prioritize safety in all features
- **Training/Education**: Emphasize continuous learning
- **Fair Wages**: Focus on compensation transparency

### Data Validation

#### IBEW-Specific Validation

```dart
class IBEWValidation {
  static bool isValidLocalNumber(int localNumber) {
    return localNumber >= 1 && localNumber <= 2500;
  }
  
  static bool isValidTicketNumber(int ticketNumber) {
    return ticketNumber > 0 && ticketNumber.toString().length <= 8;
  }
  
  static bool isValidClassification(String classification) {
    final validClassifications = [
      'Inside Wireman',
      'Journeyman Lineman', 
      'Tree Trimmer',
      'Equipment Operator',
      // ... other valid classifications
    ];
    return validClassifications.contains(classification);
  }
}
```

---

*This domain knowledge guide provides the essential context needed to build features that authentically serve IBEW electrical workers and respect union traditions and procedures.*
