
# Transformer Bank Connection Trainer - Technical Documentation

## Overview

The Transformer Bank Connection Trainer is a comprehensive Flutter widget designed for electrical training applications, specifically for I.B.E.W. journeyman electricians and apprentices. This interactive component teaches proper transformer bank wiring configurations through hands-on learning.

## Features

### 🔌 **Five Transformer Bank Types**

- **Wye-Wye**: Both primary and secondary in star configuration
- **Delta-Delta**: Both sides in delta configuration  
- **Wye-Delta**: Primary wye, secondary delta (common utility setup)
- **Delta-Wye**: Primary delta, secondary wye (generation applications)
- **Open Delta**: Emergency two-transformer operation

### 📚 **Dual Learning Modes**

- **Guided Mode**: Step-by-step instructions with explanations and safety notes
- **Quiz Mode**: Test knowledge without guidance, with immediate feedback

### ⚡ **Interactive Experience**

- Tap or drag to make electrical connections
- Real-time visual feedback with animations
- Error flash effects for incorrect connections
- Success animations for proper wiring
- Professional electrical schematic styling

### 🎯 **Difficulty Progression**

- **Beginner**: 120V/240V residential scenarios
- **Intermediate**: 240V/480V commercial applications  
- **Advanced**: Industrial high-voltage configurations

### 🏗️ **Educational Content**

- Comprehensive safety considerations for each bank type
- Common mistakes and how to avoid them
- Real-world applications and use cases
- Voltage calculations and measurements
- Phase shift indicators for applicable configurations

## Architecture

### File Structure

``` tree
lib/transformer_trainer/
├── transformer_trainer.dart          # Main export
├── widgets/
│   ├── trainer_widget.dart          # Main widget
│   ├── transformer_diagram.dart     # Interactive diagram
│   └── connection_point.dart        # Connection terminals
├── models/
│   ├── transformer_models.dart      # Data models & enums
│   └── educational_content.dart     # All educational text
├── modes/
│   ├── guided_mode.dart             # Step-by-step learning
│   └── quiz_mode.dart               # Knowledge testing
├── painters/
│   ├── base_transformer_painter.dart # Common painting logic
│   ├── wye_wye_painter.dart         # Wye-Wye configuration
│   ├── delta_delta_painter.dart     # Delta-Delta configuration
│   ├── wye_delta_painter.dart       # Wye-Delta configuration
│   ├── delta_wye_painter.dart       # Delta-Wye configuration
│   └── open_delta_painter.dart      # Open Delta configuration
├── animations/
│   ├── flash_animation.dart         # Error feedback
│   └── success_animation.dart       # Success feedback
└── state/
    └── transformer_state.dart       # State management
```

### State Management

The trainer uses Provider pattern with `TransformerTrainerState` for:

- Current training configuration (bank type, mode, difficulty)
- Connection tracking and validation
- Step progression in guided mode
- Educational content delivery

### Custom Painters

Each transformer bank type has a dedicated CustomPainter that:

- Renders authentic electrical schematic symbols
- Draws transformers, connection points, and wiring
- Shows voltage labels and phase relationships
- Indicates special characteristics (phase shifts, warnings)

## Integration Guide

### Basic Integration

```dart
import 'package:transformer_trainer/transformer_trainer.dart';

// Minimal implementation
TransformerTrainer()

// With configuration
TransformerTrainer(
  initialBankType: TransformerBankType.wyeToWye,
  initialMode: TrainingMode.guided,
  initialDifficulty: DifficultyLevel.beginner,
  onStepComplete: (step) => print('Step completed'),
  onBankComplete: (bankType) => print('Bank completed'),
  onError: (error) => print('Error occurred'),
)
```

### Advanced Integration

```dart
class CustomTrainingScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Transformer Training')),
      body: TransformerTrainer(
        onBankComplete: (bankType) {
          // Integration with app's progress tracking
          _updateUserProgress(bankType);
          _showCompletionDialog(bankType);
        },
        onStepComplete: (step) {
          // Analytics tracking
          _trackStepCompletion(step);
        },
        onError: (error) {
          // Help system integration
          _offerContextualHelp(error);
        },
      ),
    );
  }
}
```

## Educational Content

### Safety Considerations by Bank Type

**Wye-Wye Connections:**

- Ensure proper neutral grounding on primary side
- Check phase rotation before energizing
- Verify neutral isolation between primary and secondary
- Test insulation before connection

**Delta-Delta Connections:**

- No neutral point - ensure proper grounding scheme
- Check all three phases before closing
- Verify correct polarity markings
- Test for opens in delta connections

**Wye-Delta Connections:**

- Primary neutral must be properly grounded
- 30-degree phase shift - check rotation
- Verify voltage ratios match system requirements
- Check for proper isolation between primary and secondary neutrals

**Delta-Wye Connections:**

- Secondary neutral must be properly grounded
- Check for 30-degree phase shift
- Verify proper connection sequence
- Test secondary neutral for proper grounding

**Open Delta Connections:**

- Only 86.6% of normal capacity available
- Monitor for voltage unbalance under load
- Check both transformers are properly connected
- Plan for full bank restoration as soon as possible

### Common Mistakes

The trainer includes detailed coverage of common wiring errors:

- Incorrect polarity causing circulating currents
- Missing neutral connections
- Phase shift miscalculations
- Improper grounding schemes
- Voltage ratio errors

## Technical Specifications

### Dependencies

- Flutter SDK >= 3.0.0
- Dart SDK >= 2.19.0  
- Provider ^6.1.1 (state management)
- Flutter SVG ^2.0.9 (vector graphics support)

### Performance

- Lightweight implementation (<3MB total size)
- Smooth 60fps animations on modern devices
- Offline-ready with all content stored locally
- Memory efficient with lazy-loaded painters

### Compatibility

- Android API 21+ (Android 5.0+)
- iOS 11.0+
- Web support available
- Desktop support (Windows, macOS, Linux)

## Customization Options

### Voltage Scenarios

Modify `educational_content.dart` to add custom voltage levels:

```dart
static const Map<DifficultyLevel, List<VoltageScenario>> voltageScenarios = {
  DifficultyLevel.custom: [
    VoltageScenario(
      name: 'Custom 600V Industrial',
      voltages: {'primary': 25000, 'secondary_line': 600},
      description: 'High-voltage industrial application',
    ),
  ],
};
```

### Additional Bank Types

Extend the system by:

1. Adding new enum values to `TransformerBankType`
2. Creating corresponding CustomPainter classes
3. Implementing connection logic in state management
4. Adding educational content

### UI Theming

The widget respects Flutter's theme system:

- Colors adapt to app's color scheme
- Text styles follow Material Design guidelines
- Support for light/dark mode themes

## Testing

The trainer includes comprehensive test coverage:

### Unit Tests

- Connection validation logic
- State management transitions
- Educational content retrieval
- Animation controllers

### Widget Tests

- User interaction flows
- Visual feedback systems
- Mode switching functionality
- Error handling

### Integration Tests

- Complete training workflows
- Cross-platform compatibility
- Performance benchmarks

## Best Practices

### Performance Optimization

- Use `const` constructors where possible
- Implement `shouldRepaint` optimization in custom painters
- Lazy load educational content
- Dispose animation controllers properly

### Accessibility

- Semantic labels for screen readers
- High contrast color schemes
- Keyboard navigation support
- Adjustable text sizes

### User Experience

- Clear visual feedback for all interactions
- Consistent animation timing
- Logical progression through difficulty levels
- Comprehensive help system integration

## Future Enhancements

### Planned Features

- Additional transformer configurations (zigzag, Scott-T)
- Advanced fault scenario training
- Multi-language support
- Augmented reality integration
- Voice-guided instructions
- Integration with electrical calculation tools

### Extension Points

The architecture supports easy extension through:

- Plugin system for custom bank types
- Configurable difficulty parameters
- Custom animation effects
- Third-party content integration

## Support and Maintenance

### Version Management

- Semantic versioning (MAJOR.MINOR.PATCH)
- Backward compatibility maintained
- Migration guides for breaking changes

### Documentation

- Comprehensive API documentation
- Integration examples
- Video tutorials available
- Community support forums

## License and Usage

Designed specifically for the Journeyman Jobs app and I.B.E.W. electrical training applications. Commercial usage requires appropriate licensing agreements.

---

*This documentation reflects the current state of the Transformer Bank Connection Trainer as of the latest release. For the most up-to-date information, please refer to the official repository and changelog.*
