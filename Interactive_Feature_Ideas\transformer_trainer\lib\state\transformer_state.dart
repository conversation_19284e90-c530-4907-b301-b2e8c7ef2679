
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart'; // Added for Offset
import '../models/transformer_models.dart';
import '../models/educational_content.dart';

/// State management for the transformer trainer using ChangeNotifier
class TransformerTrainerState extends ChangeNotifier {
  TrainingState _currentState = const TrainingState(
    bankType: TransformerBankType.wyeToWye,
    mode: TrainingMode.guided,
    difficulty: DifficultyLevel.beginner,
  );

  /// Current training state
  TrainingState get currentState => _currentState;
  
  /// Available connection points for the current bank type
  List<ConnectionPoint> get connectionPoints => _getConnectionPoints();
  
  /// Required connections for the current bank type and step
  List<WireConnection> get requiredConnections => _getRequiredConnections();
  
  /// Current training steps (for guided mode)
  List<TrainingStep> get trainingSteps => 
      EducationalContent.getTrainingSteps(_currentState.bankType);
  
  /// Current step in guided mode
  TrainingStep? get currentStep {
    if (_currentState.mode != TrainingMode.guided) return null;
    final steps = trainingSteps;
    if (_currentState.currentStep < steps.length) {
      return steps[_currentState.currentStep];
    }
    return null;
  }

  /// Update the current training state
  void updateState(TrainingState newState) {
    _currentState = newState;
    notifyListeners();
  }

  /// Change bank type
  void setBankType(TransformerBankType bankType) {
    _currentState = _currentState.copyWith(
      bankType: bankType,
      connections: [],
      currentStep: 0,
      isComplete: false,
      completedSteps: [],
    );
    notifyListeners();
  }

  /// Change training mode
  void setMode(TrainingMode mode) {
    _currentState = _currentState.copyWith(
      mode: mode,
      connections: [],
      currentStep: 0,
      isComplete: false,
      completedSteps: [],
    );
    notifyListeners();
  }

  /// Change difficulty level
  void setDifficulty(DifficultyLevel difficulty) {
    _currentState = _currentState.copyWith(
      difficulty: difficulty,
    );
    notifyListeners();
  }

  /// Add a new connection
  void addConnection(String fromPointId, String toPointId) {
    final isCorrect = _validateConnection(fromPointId, toPointId);
    final errorReason = isCorrect ? null : _getConnectionError(fromPointId, toPointId);
    
    final newConnection = WireConnection(
      fromPointId: fromPointId,
      toPointId: toPointId,
      isCorrect: isCorrect,
      errorReason: errorReason,
    );

    final updatedConnections = [..._currentState.connections, newConnection];
    
    _currentState = _currentState.copyWith(
      connections: updatedConnections,
    );

    // Check if step is complete (guided mode)
    if (_currentState.mode == TrainingMode.guided && isCorrect) {
      _checkStepCompletion();
    }
    
    // Check if entire bank is complete
    _checkBankCompletion();
    
    notifyListeners();
  }

  /// Remove a connection
  void removeConnection(String fromPointId, String toPointId) {
    final updatedConnections = _currentState.connections
        .where((conn) => !(conn.fromPointId == fromPointId && conn.toPointId == toPointId))
        .toList();
    
    _currentState = _currentState.copyWith(
      connections: updatedConnections,
    );
    notifyListeners();
  }

  /// Clear all connections
  void clearConnections() {
    _currentState = _currentState.copyWith(
      connections: [],
      currentStep: 0,
      isComplete: false,
      completedSteps: [],
    );
    notifyListeners();
  }

  /// Get connection points for current bank type
  List<ConnectionPoint> _getConnectionPoints() {
    // Return connection points based on current bank type
    // This would be dynamically generated based on the transformer configuration
    switch (_currentState.bankType) {
      case TransformerBankType.wyeToWye:
        return _getWyeToWyeConnectionPoints();
      case TransformerBankType.deltaToDelta:
        return _getDeltaToDeltaConnectionPoints();
      case TransformerBankType.wyeToDelta:
        return _getWyeToDeltaConnectionPoints();
      case TransformerBankType.deltaToWye:
        return _getDeltaToWyeConnectionPoints();
      case TransformerBankType.openDelta:
        return _getOpenDeltaConnectionPoints();
    }
  }

  /// Validate if a connection is correct
  bool _validateConnection(String fromPointId, String toPointId) {
    // Implementation depends on current bank type and training rules
    // This is a simplified validation - real implementation would be more complex
    final requiredConns = _getRequiredConnections();
    return requiredConns.any((conn) => 
        (conn.fromPointId == fromPointId && conn.toPointId == toPointId) ||
        (conn.fromPointId == toPointId && conn.toPointId == fromPointId));
  }

  /// Get error message for incorrect connection
  String _getConnectionError(String fromPointId, String toPointId) {
    // Return specific error message based on the incorrect connection attempt
    return 'This connection is not correct for the current transformer bank configuration.';
  }

  /// Check if current step is complete (guided mode)
  void _checkStepCompletion() {
    if (_currentState.mode != TrainingMode.guided) return;
    
    final step = currentStep;
    if (step == null) return;
    
    // Check if all required connections for this step are made
    final requiredForStep = step.requiredConnections;
    final madeConnections = _currentState.connections
        .where((conn) => conn.isCorrect)
        .map((conn) => '${conn.fromPointId}_to_${conn.toPointId}')
        .toSet();
    
    final allStepConnectionsMade = requiredForStep.every(
        (required) => madeConnections.contains(required));
    
    if (allStepConnectionsMade) {
      final updatedCompletedSteps = [..._currentState.completedSteps, step.stepNumber.toString()];
      _currentState = _currentState.copyWith(
        currentStep: _currentState.currentStep + 1,
        completedSteps: updatedCompletedSteps,
      );
    }
  }

  /// Check if entire bank is complete
  void _checkBankCompletion() {
    final requiredConnections = _getRequiredConnections();
    final correctConnections = _currentState.connections.where((conn) => conn.isCorrect).length;
    
    if (correctConnections >= requiredConnections.length) {
      _currentState = _currentState.copyWith(isComplete: true);
    }
  }

  /// Get required connections for current bank type
  List<WireConnection> _getRequiredConnections() {
    // Return all required connections for the current bank type
    // This would be different for each transformer configuration
    switch (_currentState.bankType) {
      case TransformerBankType.wyeToWye:
        return _getWyeToWyeRequiredConnections();
      case TransformerBankType.deltaToDelta:
        return _getDeltaToDeltaRequiredConnections();
      case TransformerBankType.wyeToDelta:
        return _getWyeToDeltaRequiredConnections();
      case TransformerBankType.deltaToWye:
        return _getDeltaToWyeRequiredConnections();
      case TransformerBankType.openDelta:
        return _getOpenDeltaRequiredConnections();
    }
  }

  // Connection point generators for each bank type
  List<ConnectionPoint> _getWyeToWyeConnectionPoints() {
    return [
      // Primary connections
      const ConnectionPoint(id: 'phase_a', position: Offset(50, 100), label: 'Phase A', type: ConnectionType.primary, isInput: true),
      const ConnectionPoint(id: 'phase_b', position: Offset(50, 200), label: 'Phase B', type: ConnectionType.primary, isInput: true),
      const ConnectionPoint(id: 'phase_c', position: Offset(50, 300), label: 'Phase C', type: ConnectionType.primary, isInput: true),
      
      // Transformer 1 primary
      const ConnectionPoint(id: 't1_h1', position: Offset(150, 100), label: 'T1-H1', type: ConnectionType.primary, isInput: false),
      const ConnectionPoint(id: 't1_h2', position: Offset(200, 100), label: 'T1-H2', type: ConnectionType.primary, isInput: false),
      
      // Additional connection points would be defined here...
    ];
  }

  List<ConnectionPoint> _getDeltaToDeltaConnectionPoints() {
    // TODO: Implement delta-delta connection points
    return [];
  }

  List<ConnectionPoint> _getWyeToDeltaConnectionPoints() {
    // TODO: Implement wye-delta connection points
    return [];
  }

  List<ConnectionPoint> _getDeltaToWyeConnectionPoints() {
    // TODO: Implement delta-wye connection points
    return [];
  }

  List<ConnectionPoint> _getOpenDeltaConnectionPoints() {
    // TODO: Implement open-delta connection points
    return [];
  }

  // Required connection generators for each bank type
  List<WireConnection> _getWyeToWyeRequiredConnections() {
    return [
      const WireConnection(fromPointId: 'phase_a', toPointId: 't1_h1', isCorrect: true),
      const WireConnection(fromPointId: 'phase_b', toPointId: 't2_h1', isCorrect: true),
      const WireConnection(fromPointId: 'phase_c', toPointId: 't3_h1', isCorrect: true),
      // Additional required connections...
    ];
  }

  List<WireConnection> _getDeltaToDeltaRequiredConnections() {
    // TODO: Implement delta-delta required connections
    return [];
  }

  List<WireConnection> _getWyeToDeltaRequiredConnections() {
    // TODO: Implement wye-delta required connections
    return [];
  }

  List<WireConnection> _getDeltaToWyeRequiredConnections() {
    // TODO: Implement delta-wye required connections
    return [];
  }

  List<WireConnection> _getOpenDeltaRequiredConnections() {
    // TODO: Implement open-delta required connections
    return [];
  }
}
