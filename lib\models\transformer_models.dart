import 'package:flutter/material.dart';

/// Represents different transformer bank configurations
enum TransformerBankType {
  wyeToWye,
  deltaToDel<PERSON>,
  wyeToDelta,
  deltaToWye,
  openDelta,
}

/// Training mode - guided learning vs quiz testing
enum TrainingMode {
  guided,  // Step-by-step with hints
  quiz,    // Test knowledge without guidance
}

/// Difficulty levels with different voltage scenarios
enum DifficultyLevel {
  beginner,     // 120V/240V basic residential
  intermediate, // 240V/480V light commercial  
  advanced,     // 480V+ industrial scenarios
}

/// Connection mode for the workbench
enum ConnectionMode {
  stickyKeys,  // Tap to connect
  dragAndDrop, // Drag to connect
}

/// Represents a connection point on the transformer diagram
class ConnectionPoint {
  final String id;
  final Offset position;
  final String label;
  final ConnectionType type;
  final bool isInput;
  
  const ConnectionPoint({
    required this.id,
    required this.position,
    required this.label,
    required this.type,
    required this.isInput,
  });
}

/// Types of electrical connections
enum ConnectionType {
  primary,    // High voltage side (H1, H2)
  secondary,  // Low voltage side (X1, X2, X3)
  neutral,    // Neutral connections
  ground,     // Ground connections
}

/// Represents a wire connection between two points
class WireConnection {
  final String fromPointId;
  final String toPointId;
  final bool isCorrect;
  final String? errorReason;
  final Color color;
  final String phase;
  
  const WireConnection({
    required this.fromPointId,
    required this.toPointId,
    required this.isCorrect,
    this.errorReason,
    this.color = Colors.red,
    this.phase = 'A',
  });
}

/// Represents the current state of the training session
class TrainingState {
  final TransformerBankType bankType;
  final TrainingMode mode;
  final DifficultyLevel difficulty;
  final List<WireConnection> connections;
  final int currentStep;
  final bool isComplete;
  final List<String> completedSteps;
  
  const TrainingState({
    required this.bankType,
    required this.mode,
    required this.difficulty,
    this.connections = const [],
    this.currentStep = 0,
    this.isComplete = false,
    this.completedSteps = const [],
  });
  
  TrainingState copyWith({
    TransformerBankType? bankType,
    TrainingMode? mode,
    DifficultyLevel? difficulty,
    List<WireConnection>? connections,
    int? currentStep,
    bool? isComplete,
    List<String>? completedSteps,
  }) {
    return TrainingState(
      bankType: bankType ?? this.bankType,
      mode: mode ?? this.mode,
      difficulty: difficulty ?? this.difficulty,
      connections: connections ?? this.connections,
      currentStep: currentStep ?? this.currentStep,
      isComplete: isComplete ?? this.isComplete,
      completedSteps: completedSteps ?? this.completedSteps,
    );
  }
}

/// Voltage scenario for different difficulty levels
class VoltageScenario {
  final String name;
  final Map<String, int> voltages;
  final String description;
  
  const VoltageScenario({
    required this.name,
    required this.voltages,
    required this.description,
  });
}

/// Step in the guided training mode
class TrainingStep {
  final int stepNumber;
  final String instruction;
  final String explanation;
  final List<String> requiredConnections;
  final String? safetyNote;
  final String? commonMistake;
  
  const TrainingStep({
    required this.stepNumber,
    required this.instruction,
    required this.explanation,
    required this.requiredConnections,
    this.safetyNote,
    this.commonMistake,
  });

  Map<String, dynamic> toJson() {
    return {
      'stepNumber': stepNumber,
      'instruction': instruction,
      'explanation': explanation,
      'requiredConnections': requiredConnections,
      'safetyNote': safetyNote,
      'commonMistake': commonMistake,
    };
  }

  factory TrainingStep.fromJson(Map<String, dynamic> json) {
    return TrainingStep(
      stepNumber: json['stepNumber'] as int,
      instruction: json['instruction'] as String,
      explanation: json['explanation'] as String,
      requiredConnections: List<String>.from(json['requiredConnections'] as List),
      safetyNote: json['safetyNote'] as String?,
      commonMistake: json['commonMistake'] as String?,
    );
  }
}