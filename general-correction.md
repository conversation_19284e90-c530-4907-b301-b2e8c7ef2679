# General Corrections

## *Home page*

- Everything on the card that is related to a back end query like the jobs or local detail needs to be rich text so that the descriptive word stays and is hard coded but then the value which is the variable depending on the job or the local for example:

`Location:` [location]
`Per Diem:` [perDiem]
`Wages:` [wage]
`Local:` [local]
`Hours:` [hours]
`Classification:` [classification]
`Duration:` [duration]
`Start Date:` [startDate]
`Type of Work:` [typeOfWork]
`Construction Type:` [constructionType]
`contractor:` [contractor]

This shows The value inside the square brackets is the variable that is being called from the back end The text inside of the single tick or whatever that is is hard coded and never changes

- remove The word standard at the top right corner of each card
- Remove the hard coded '$' and 'day' from the per diem
- Add the word 'wages' to the left of the dollar amount.
- each type of data that is queried fron the backend must be shown with rich text. The first span will stay consistant, while the second span will be the backend variable.
- Please reference the image at @assets\images\job-card.png. This format must remain constant for job cards and local cards
- Remove the word competitive where the wages is supposed to be if a value is missing then don't display any of it nor replace it with anything else.
- Remove the notification icon the little bell remove the one that is to the right of Welcome back <PERSON> and keep the notification icon at the top of the screen on the app banner or whatever it is
- I need to add the copper border around each of the cards to maintain a consistent app theme.

## *Jobs page*

- The font or text size for the two buttons Detail and Apply at the bottom of the card is too big so you can't see then the text so that needs to be shrunk
- When you click on a job when you click on the card instead of it being a small pop up like the jobs on the home page it's like an entire screen overlay which is fine however that overlay needs to follow the app theme so the green and the light orange around the wages and hours needs to be removed and replaced with something related to the app theme - Remove the safety reminder
  
## *Locals page*

- On the local screen the locals need to be sorted numerically from lowest to highest meaning like one, 2, 3, 4, 5 and so on Not how it is now where it's local one, local 10, local 100, local 1000.
- The popup for the individual local needs to follow the apps design theme.
