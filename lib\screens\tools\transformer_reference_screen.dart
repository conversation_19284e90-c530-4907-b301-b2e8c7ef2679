import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../design_system/app_theme.dart';
import '../../models/transformer_models.dart';
import 'transformer_workbench_screen.dart';

class TransformerReferenceScreen extends StatefulWidget {
  const TransformerReferenceScreen({Key? key}) : super(key: key);

  @override
  State<TransformerReferenceScreen> createState() =>
      _TransformerReferenceScreenState();
}

class _TransformerReferenceScreenState extends State<TransformerReferenceScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.offWhite,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryNavy,
        foregroundColor: AppTheme.white,
        title: const Text(
          'Reference Mode',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Implement search functionality
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Implement settings
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildInfoBanner(),
            const SizedBox(height: AppTheme.spacingLg),
            _buildSinglePotSection(),
            const SizedBox(height: AppTheme.spacingLg),
            _buildTwoPotSection(),
            const SizedBox(height: AppTheme.spacingLg),
            _buildThreePotSection(),
            const SizedBox(height: AppTheme.spacingXl),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoBanner() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(AppTheme.spacingMd),
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.infoBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        border: Border.all(
          color: AppTheme.infoBlue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppTheme.infoBlue,
            size: AppTheme.iconMd,
          ),
          const SizedBox(width: AppTheme.spacingMd),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'SELECT A CONFIGURATION TO EXPLORE',
                  style: TextStyle(
                    color: AppTheme.primaryNavy,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: AppTheme.spacingXs),
                Text(
                  'Tap any transformer bank to view detailed diagrams and learn about each component.',
                  style: TextStyle(
                    color: AppTheme.textSecondary,
                    fontSize: 13,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSinglePotSection() {
    return _buildSection(
      title: 'SINGLE POT TRANSFORMERS',
      child: _buildSinglePotCard(),
    );
  }

  Widget _buildTwoPotSection() {
    return _buildSection(
      title: 'TWO POT BANKS',
      child: _buildTwoPotCard(),
    );
  }

  Widget _buildThreePotSection() {
    return _buildSection(
      title: 'THREE POT BANKS',
      child: Column(
        children: [
          Row(
            children: [
              Expanded(child: _buildThreePotCard('WYE-WYE', _buildWyeWyeDiagram())),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(child: _buildThreePotCard('DELTA-DELTA', _buildDeltaDeltaDiagram())),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Row(
            children: [
              Expanded(child: _buildThreePotCard('WYE-DELTA', _buildWyeDeltaDiagram())),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(child: _buildThreePotCard('DELTA-WYE', _buildDeltaWyeDiagram())),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection({required String title, required Widget child}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: AppTheme.textSecondary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          child,
        ],
      ),
    );
  }

  Widget _buildSinglePotCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        boxShadow: const [AppTheme.shadowSm],
        border: Border.all(color: AppTheme.borderLight),
      ),
      child: Row(
        children: [
          // Transformer diagram
          Container(
            width: 120,
            height: 80,
            decoration: BoxDecoration(
              color: AppTheme.lightGray,
              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
            ),
            child: CustomPaint(
              painter: SinglePotDiagramPainter(),
            ),
          ),
          const SizedBox(width: AppTheme.spacingMd),
          // Information
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'SINGLE POT',
                  style: TextStyle(
                    color: AppTheme.primaryNavy,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXs),
                const Text(
                  '120V/240V Residential',
                  style: TextStyle(
                    color: AppTheme.accentCopper,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingSm),
                const Text(
                  '• Common household setup\n• Split-phase secondary\n• Center-tapped transformer',
                  style: TextStyle(
                    color: AppTheme.textLight,
                    fontSize: 12,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMd),
                Align(
                  alignment: Alignment.centerRight,
                  child: _buildViewDetailsButton(() {
                    _navigateToWorkbench('Single Pot');
                  }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTwoPotCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        boxShadow: const [AppTheme.shadowSm],
        border: Border.all(color: AppTheme.borderLight),
      ),
      child: Row(
        children: [
          // Transformer diagram
          Container(
            width: 120,
            height: 80,
            decoration: BoxDecoration(
              color: AppTheme.lightGray,
              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
            ),
            child: CustomPaint(
              painter: OpenDeltaDiagramPainter(),
            ),
          ),
          const SizedBox(width: AppTheme.spacingMd),
          // Information
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'OPEN DELTA',
                  style: TextStyle(
                    color: AppTheme.primaryNavy,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingXs),
                const Text(
                  '240V Three-Phase (2 Pots)',
                  style: TextStyle(
                    color: AppTheme.accentCopper,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingSm),
                const Text(
                  '• V-V Connection\n• 86.6% of full capacity\n• Emergency configuration',
                  style: TextStyle(
                    color: AppTheme.textLight,
                    fontSize: 12,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingMd),
                Align(
                  alignment: Alignment.centerRight,
                  child: _buildViewDetailsButton(() {
                    _navigateToWorkbench('Open Delta');
                  }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThreePotCard(String title, Widget diagram) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        boxShadow: const [AppTheme.shadowSm],
        border: Border.all(color: AppTheme.borderLight),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(
              color: AppTheme.primaryNavy,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Container(
            width: double.infinity,
            height: 80,
            decoration: BoxDecoration(
              color: AppTheme.lightGray,
              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
            ),
            child: diagram,
          ),
          const SizedBox(height: AppTheme.spacingMd),
          _buildViewDetailsButton(() {
            _navigateToWorkbench(title);
          }),
        ],
      ),
    );
  }

  Widget _buildViewDetailsButton(VoidCallback onPressed) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: AppTheme.accentCopper,
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMd,
          vertical: AppTheme.spacingSm,
        ),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'View',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 13,
            ),
          ),
          SizedBox(width: AppTheme.spacingXs),
          Icon(
            Icons.arrow_forward,
            size: AppTheme.iconSm,
          ),
        ],
      ),
    );
  }

  // Diagram widgets
  Widget _buildWyeWyeDiagram() {
    return CustomPaint(painter: WyeWyeDiagramPainter());
  }

  Widget _buildDeltaDeltaDiagram() {
    return CustomPaint(painter: DeltaDeltaDiagramPainter());
  }

  Widget _buildWyeDeltaDiagram() {
    return CustomPaint(painter: WyeDeltaDiagramPainter());
  }

  Widget _buildDeltaWyeDiagram() {
    return CustomPaint(painter: DeltaWyeDiagramPainter());
  }

  void _navigateToWorkbench(String configurationType) {
    final bankType = _mapConfigurationToBankType(configurationType);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TransformerWorkbenchScreen(
          bankType: bankType,
          mode: TrainingMode.guided,
          difficulty: DifficultyLevel.beginner,
          isReferenceMode: true,
        ),
      ),
    );
  }

  TransformerBankType _mapConfigurationToBankType(String configurationType) {
    switch (configurationType.toUpperCase()) {
      case 'WYE-WYE':
        return TransformerBankType.wyeToWye;
      case 'DELTA-DELTA':
        return TransformerBankType.deltaToDelta;
      case 'WYE-DELTA':
        return TransformerBankType.wyeToDelta;
      case 'DELTA-WYE':
        return TransformerBankType.deltaToWye;
      case 'OPEN DELTA':
        return TransformerBankType.openDelta;
      default:
        // Default to wyeToWye for single pot or unknown configurations
        return TransformerBankType.wyeToWye;
    }
  }
}

// Custom painters for transformer diagrams
class SinglePotDiagramPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.primaryNavy
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw transformer symbol (rectangle)
    final rect = Rect.fromCenter(
      center: center,
      width: size.width * 0.4,
      height: size.height * 0.6,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      paint,
    );
    
    // Draw T1 label
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'T1',
        style: TextStyle(
          color: AppTheme.primaryNavy,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class OpenDeltaDiagramPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.primaryNavy
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw two transformer rectangles
    final t1Rect = Rect.fromCenter(
      center: Offset(center.dx - 25, center.dy),
      width: 20,
      height: 40,
    );
    final t2Rect = Rect.fromCenter(
      center: Offset(center.dx + 25, center.dy),
      width: 20,
      height: 40,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(t1Rect, const Radius.circular(2)),
      paint,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(t2Rect, const Radius.circular(2)),
      paint,
    );
    
    // Draw connection lines (V-V)
    canvas.drawLine(
      Offset(t1Rect.right, t1Rect.top),
      Offset(t2Rect.left, t2Rect.top),
      paint,
    );
    canvas.drawLine(
      Offset(t1Rect.right, t1Rect.bottom),
      Offset(t2Rect.left, t2Rect.bottom),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WyeWyeDiagramPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.primaryNavy
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw Y-shaped connection (3 lines from center)
    const angles = [0, 2.09, 4.19]; // 120 degrees apart
    for (final angle in angles) {
      final start = center;
      final end = Offset(
        center.dx + 25 * math.cos(angle),
        center.dy + 25 * math.sin(angle),
      );
      canvas.drawLine(start, end, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class DeltaDeltaDiagramPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.primaryNavy
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw triangle (delta)
    final path = Path();
    const radius = 20.0;
    const angles = [1.57, 3.67, 5.76]; // Triangle points
    
    path.moveTo(
      center.dx + radius * math.cos(angles[0]),
      center.dy + radius * math.sin(angles[0]),
    );
    
    for (int i = 1; i < angles.length; i++) {
      path.lineTo(
        center.dx + radius * math.cos(angles[i]),
        center.dy + radius * math.sin(angles[i]),
      );
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WyeDeltaDiagramPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.primaryNavy
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw Y on left side
    final leftCenter = Offset(center.dx - 15, center.dy);
    const angles = [0, 2.09, 4.19];
    for (final angle in angles) {
      final start = leftCenter;
      final end = Offset(
        leftCenter.dx + 12 * math.cos(angle),
        leftCenter.dy + 12 * math.sin(angle),
      );
      canvas.drawLine(start, end, paint);
    }
    
    // Draw triangle on right side
    final rightCenter = Offset(center.dx + 15, center.dy);
    final path = Path();
    const radius = 12.0;
    const triangleAngles = [1.57, 3.67, 5.76];
    
    path.moveTo(
      rightCenter.dx + radius * math.cos(triangleAngles[0]),
      rightCenter.dy + radius * math.sin(triangleAngles[0]),
    );
    
    for (int i = 1; i < triangleAngles.length; i++) {
      path.lineTo(
        rightCenter.dx + radius * math.cos(triangleAngles[i]),
        rightCenter.dy + radius * math.sin(triangleAngles[i]),
      );
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class DeltaWyeDiagramPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.primaryNavy
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw triangle on left side
    final leftCenter = Offset(center.dx - 15, center.dy);
    final path = Path();
    const radius = 12.0;
    const triangleAngles = [1.57, 3.67, 5.76];
    
    path.moveTo(
      leftCenter.dx + radius * math.cos(triangleAngles[0]),
      leftCenter.dy + radius * math.sin(triangleAngles[0]),
    );
    
    for (int i = 1; i < triangleAngles.length; i++) {
      path.lineTo(
        leftCenter.dx + radius * math.cos(triangleAngles[i]),
        leftCenter.dy + radius * math.sin(triangleAngles[i]),
      );
    }
    path.close();
    canvas.drawPath(path, paint);
    
    // Draw Y on right side
    final rightCenter = Offset(center.dx + 15, center.dy);
    const angles = [0, 2.09, 4.19];
    for (final angle in angles) {
      final start = rightCenter;
      final end = Offset(
        rightCenter.dx + 12 * math.cos(angle),
        rightCenter.dy + 12 * math.sin(angle),
      );
      canvas.drawLine(start, end, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}