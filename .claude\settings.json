{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(uv:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(npm:*)", "Bash(ls:*)", "Bash(cp:*)", "Write", "Edit", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(curl:*)"], "deny": []}, "hooks": {"PreToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/pre_tool_use.py"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/post_tool_use.py"}]}], "Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/notification.py --notify"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/stop.py --chat"}]}], "SubagentStop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/subagent_stop.py"}]}], "UserPromptSubmit": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/user_prompt_submit.py --log-only"}]}], "PreCompact": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/pre_compact.py"}]}], "SessionStart": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/session_start.py"}]}]}}