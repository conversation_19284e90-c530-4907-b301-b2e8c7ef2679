# Frontend Architecture

*Detailed Flutter frontend architecture for the Journeyman Jobs application*

## 📱 Flutter Application Structure

### Feature-Based Architecture

The Journeyman Jobs app follows a **feature-based architecture** organized around electrical worker workflows and IBEW industry patterns:

``` tree
lib/
├── main.dart                          # Application entry point
├── design_system/                     # Electrical theme and components
├── electrical_components/              # Industry-specific UI components
├── screens/                           # Feature screens by domain
├── providers/                         # State management
├── services/                          # Business logic and API integration
├── models/                            # Data structures
├── navigation/                        # Routing configuration
├── utils/                             # Helper functions and extensions
└── legacy/                            # FlutterFlow migration artifacts
```

### Domain-Driven Organization

Each major feature area corresponds to electrical worker activities:

- **Authentication**: Union member identification and security
- **Jobs**: Core job discovery and application workflows
- **Locals**: IBEW union directory and networking
- **Storm**: Emergency response and storm work
- **Tools**: Electrical calculators and professional resources
- **Settings**: User preferences and account management

## 🎭 State Management Architecture

### Provider Pattern Implementation

The app uses the **Provider pattern** with **ChangeNotifier** for reactive state management:

```dart
// Global state architecture
MultiProvider(
  providers: [
    // Core services (singletons)
    Provider<AuthService>(create: (_) => AuthService()),
    Provider<ResilientFirestoreService>(create: (_) => ResilientFirestoreService()),
    
    // Connectivity monitoring
    ChangeNotifierProvider<ConnectivityService>(create: (_) => ConnectivityService()),
    
    // Feature-specific state
    ChangeNotifierProvider<JobFilterProvider>(create: (_) => JobFilterProvider()),
    
    // Main app state (dependent on services)
    ChangeNotifierProxyProvider3<AuthService, ResilientFirestoreService, ConnectivityService, AppStateProvider>(
      create: (context) => AppStateProvider(
        context.read<AuthService>(),
        context.read<ResilientFirestoreService>(),
      ),
      update: (context, authService, firestoreService, connectivityService, previous) =>
          previous ?? AppStateProvider(authService, firestoreService),
    ),
  ],
  child: MaterialApp.router(/* ... */),
)
```

### State Provider Hierarchy

#### **AppStateProvider** (Global Application State)

```dart
class AppStateProvider extends ChangeNotifier {
  final AuthService _authService;
  final ResilientFirestoreService _firestoreService;
  
  // Global state properties
  User? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;
  UserModel? _userProfile;
  
  // Getters for reactive UI updates
  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  UserModel? get userProfile => _userProfile;
  
  // Authentication state management
  void _initializeAuthListener() {
    _authService.authStateChanges.listen((User? user) {
      _currentUser = user;
      if (user != null) {
        _loadUserProfile();
      } else {
        _userProfile = null;
      }
      notifyListeners();
    });
  }
}
```

#### **JobFilterProvider** (Job Search State)

```dart
class JobFilterProvider extends ChangeNotifier {
  // Filter state
  FilterCriteria _currentFilter = FilterCriteria();
  List<Job> _allJobs = [];
  List<Job> _filteredJobs = [];
  bool _isLoading = false;
  
  // Getters
  FilterCriteria get currentFilter => _currentFilter;
  List<Job> get filteredJobs => _filteredJobs;
  bool get isLoading => _isLoading;
  
  // Filter operations
  void updateLocationFilter(String? location) {
    _currentFilter = _currentFilter.copyWith(location: location);
    _applyFilters();
    notifyListeners();
  }
  
  void updateWageRange(double? minWage, double? maxWage) {
    _currentFilter = _currentFilter.copyWith(
      minWage: minWage,
      maxWage: maxWage,
    );
    _applyFilters();
    notifyListeners();
  }
}
```

### Reactive UI Patterns

**Consumer Pattern for Reactive Updates**

```dart
// Reactive job list that updates when filters change
Consumer<JobFilterProvider>(
  builder: (context, jobProvider, child) {
    if (jobProvider.isLoading) {
      return const ElectricalRotationMeter(
        label: 'Loading jobs...',
        size: 80,
      );
    }
    
    return ListView.builder(
      itemCount: jobProvider.filteredJobs.length,
      itemBuilder: (context, index) {
        final job = jobProvider.filteredJobs[index];
        return JobCard(
          job: job,
          onTap: () => _navigateToJobDetails(job),
        );
      },
    );
  },
)
```

**Selector Pattern for Performance Optimization**

```dart
// Only rebuild when specific property changes
Selector<AppStateProvider, bool>(
  selector: (context, provider) => provider.isLoading,
  builder: (context, isLoading, child) {
    return isLoading 
      ? const CircularProgressIndicator()
      : child!;
  },
  child: const JobsList(),
)
```

## 🗺️ Navigation Architecture

### go_router Configuration

The app uses **go_router** for type-safe, declarative navigation:

```dart
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    redirect: _authGuard,
    routes: [
      // Public routes (no authentication required)
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Protected routes with bottom navigation shell
      ShellRoute(
        builder: (context, state, child) => NavBarPage(child: child),
        routes: [
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),
          GoRoute(
            path: '/jobs',
            name: 'jobs',
            builder: (context, state) => const JobsScreen(),
          ),
          // ... other main navigation routes
        ],
      ),
      
      // Modal routes (outside main navigation)
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
      ),
    ],
  );
}
```

### Navigation Patterns

**Bottom Navigation Integration**

```dart
class NavBarPage extends StatelessWidget {
  final Widget child;
  
  @override
  Widget build(BuildContext context) {
    final currentLocation = GoRouterState.of(context).matchedLocation;
    final currentIndex = AppRouter.getTabIndex(currentLocation);
    
    return Scaffold(
      body: child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: currentIndex,
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppTheme.white,
        selectedItemColor: AppTheme.accentCopper,
        unselectedItemColor: AppTheme.mediumGray,
        onTap: (index) {
          final route = AppRouter.getRouteForTab(index);
          context.go(route);
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.work_outline),
            activeIcon: Icon(Icons.work),
            label: 'Jobs',
          ),
          // ... other navigation items
        ],
      ),
    );
  }
}
```

**Authentication Guards**

```dart
String? _authGuard(BuildContext context, GoRouterState state) {
  final user = FirebaseAuth.instance.currentUser;
  final isAuthenticated = user != null;
  final location = state.matchedLocation;
  
  final publicRoutes = ['/splash', '/welcome', '/auth', '/forgot-password'];
  
  // Redirect unauthenticated users to welcome screen
  if (!isAuthenticated && !publicRoutes.contains(location)) {
    return '/welcome';
  }
  
  return null; // No redirection needed
}
```

## 🎨 Component Architecture

### Atomic Design Principles

Components are organized using atomic design methodology adapted for electrical industry use:

``` tree
design_system/
├── atoms/                    # Basic building blocks
│   ├── jj_button.dart       # Custom buttons with electrical styling
│   ├── jj_text_field.dart   # Input fields with copper accents
│   └── jj_icon.dart         # Electrical industry icons
├── molecules/               # Simple component groups
│   ├── job_card.dart        # Job posting display card
│   ├── local_card.dart      # IBEW local union card
│   └── filter_chip.dart     # Job filter selections
├── organisms/               # Complex component groups
│   ├── job_list.dart        # Complete job listing with pagination
│   ├── storm_dashboard.dart # Weather radar and emergency jobs
│   └── calculator_grid.dart # Electrical tool collection
└── templates/               # Page-level layouts
    ├── main_layout.dart     # Standard app layout with navigation
    └── modal_layout.dart    # Modal dialog layouts
```

### Custom Component Examples

**JobCard Component** (Molecule Level)

```dart
class JobCard extends StatelessWidget {
  final Job job;
  final VoidCallback? onTap;
  final bool isStormWork;
  
  const JobCard({
    Key? key,
    required this.job,
    this.onTap,
    this.isStormWork = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isStormWork ? 8 : 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.radiusLg),
            border: isStormWork 
              ? Border.all(color: AppTheme.errorRed, width: 2)
              : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: AppTheme.spacingSm),
                _buildJobDetails(),
                const SizedBox(height: AppTheme.spacingSm),
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Row(
      children: [
        if (isStormWork) ...[
          Icon(
            Icons.warning,
            color: AppTheme.errorRed,
            size: AppTheme.iconSm,
          ),
          const SizedBox(width: AppTheme.spacingXs),
        ],
        Expanded(
          child: Text(
            job.company,
            style: AppTheme.titleLarge.copyWith(
              color: AppTheme.primaryNavy,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (job.wage != null)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingSm,
              vertical: AppTheme.spacingXs,
            ),
            decoration: BoxDecoration(
              color: AppTheme.accentCopper,
              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
            ),
            child: Text(
              '\$${job.wage?.toStringAsFixed(2)}/hr',
              style: AppTheme.labelMedium.copyWith(
                color: AppTheme.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }
}
```

### Electrical Component Integration

**Custom Electrical UI Components**

```dart
// Integration of electrical-themed components
class ElectricalLoadingIndicator extends StatelessWidget {
  final String message;
  final LoadingType type;
  
  @override
  Widget build(BuildContext context) {
    switch (type) {
      case LoadingType.sineWave:
        return ThreePhaseSineWaveLoader(
          width: 200,
          height: 60,
          label: message,
        );
      case LoadingType.rotationMeter:
        return ElectricalRotationMeter(
          size: 80,
          label: message,
        );
      case LoadingType.powerLine:
        return PowerLineLoader(
          width: 300,
          height: 80,
          label: message,
        );
    }
  }
}
```

## 📊 Performance Architecture

### Lazy Loading and Code Splitting

**Screen-Level Lazy Loading**

```dart
// Lazy load screens to improve initial app startup
class AppRouter {
  static final _screenLoaders = <String, Widget Function()>{
    '/calculator': () => const ElectricalCalculatorsScreen(),
    '/transformer-training': () => const TransformerTrainingScreen(),
    '/storm': () => const StormScreen(),
  };
  
  static Widget _lazyLoadScreen(String route) {
    return FutureBuilder<Widget>(
      future: Future.microtask(() => _screenLoaders[route]!()),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return snapshot.data!;
        }
        return const ElectricalRotationMeter(
          label: 'Loading...',
          size: 60,
        );
      },
    );
  }
}
```

### Memory Management

**Efficient List Rendering**

```dart
class VirtualJobList extends StatelessWidget {
  final List<Job> jobs;
  
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      // Only build visible items plus small buffer
      itemCount: jobs.length,
      itemExtent: 120, // Fixed height for better performance
      cacheExtent: 1000, // Cache extra items for smooth scrolling
      itemBuilder: (context, index) {
        // Use const constructors where possible
        return JobCard(
          key: ValueKey(jobs[index].id),
          job: jobs[index],
        );
      },
    );
  }
}
```

**Image Optimization**

```dart
class OptimizedNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  
  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: BoxFit.cover,
      memCacheWidth: width?.round(),
      memCacheHeight: height?.round(),
      placeholder: (context, url) => Container(
        color: AppTheme.lightGray,
        child: const CircularProgressIndicator(),
      ),
      errorWidget: (context, url, error) => Container(
        color: AppTheme.lightGray,
        child: Icon(
          Icons.error_outline,
          color: AppTheme.errorRed,
        ),
      ),
    );
  }
}
```

## 🔄 Error Handling Architecture

### Centralized Error Management

**Global Error Handler**

```dart
class ErrorHandler {
  static void handleError(dynamic error, StackTrace stackTrace) {
    // Log error for debugging
    debugPrint('Error: $error\nStack trace: $stackTrace');
    
    // Report to crash analytics
    FirebaseCrashlytics.instance.recordError(error, stackTrace);
    
    // Show user-friendly message
    if (error is FirebaseException) {
      _handleFirebaseError(error);
    } else if (error is NetworkException) {
      _handleNetworkError(error);
    } else {
      _handleGenericError(error);
    }
  }
  
  static void _handleFirebaseError(FirebaseException error) {
    final message = switch (error.code) {
      'permission-denied' => 'You don\'t have permission to access this data.',
      'unavailable' => 'Service is temporarily unavailable. Please try again.',
      'not-found' => 'The requested information was not found.',
      _ => 'A service error occurred. Please try again.',
    };
    
    _showErrorToast(message);
  }
}
```

**Error Boundary Widget**

```dart
class ErrorBoundary extends StatelessWidget {
  final Widget child;
  final Widget Function(Object error)? errorBuilder;
  
  const ErrorBoundary({
    Key? key,
    required this.child,
    this.errorBuilder,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        try {
          return child;
        } catch (error, stackTrace) {
          ErrorHandler.handleError(error, stackTrace);
          
          return errorBuilder?.call(error) ?? 
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppTheme.errorRed,
                  ),
                  SizedBox(height: AppTheme.spacingMd),
                  Text(
                    'Something went wrong',
                    style: AppTheme.titleLarge,
                  ),
                ],
              ),
            );
        }
      },
    );
  }
}
```

## 🎯 Industry-Specific Patterns

### IBEW-Focused UI Patterns

**Classification-Aware Components**

```dart
class ClassificationChip extends StatelessWidget {
  final Classification classification;
  final bool isSelected;
  final VoidCallback? onTap;
  
  @override
  Widget build(BuildContext context) {
    final config = _getClassificationConfig(classification);
    
    return FilterChip(
      label: Text(config.displayName),
      avatar: Icon(
        config.icon,
        size: AppTheme.iconSm,
        color: isSelected ? AppTheme.white : config.color,
      ),
      selected: isSelected,
      onSelected: onTap != null ? (_) => onTap!() : null,
      backgroundColor: config.lightColor,
      selectedColor: config.color,
      labelStyle: AppTheme.labelMedium.copyWith(
        color: isSelected ? AppTheme.white : config.color,
      ),
    );
  }
  
  _ClassificationConfig _getClassificationConfig(Classification classification) {
    return switch (classification) {
      Classification.insideWireman => _ClassificationConfig(
        displayName: 'Inside Wireman',
        icon: Icons.electrical_services,
        color: AppTheme.accentCopper,
        lightColor: AppTheme.accentCopper.withOpacity(0.1),
      ),
      Classification.journeymanLineman => _ClassificationConfig(
        displayName: 'Journeyman Lineman',
        icon: Icons.power_outlined,
        color: AppTheme.primaryNavy,
        lightColor: AppTheme.primaryNavy.withOpacity(0.1),
      ),
      // ... other classifications
    };
  }
}
```

### Mobile-First Electrical Worker UX

**One-Handed Operation Optimization**

```dart
class MobileOptimizedButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final ButtonType type;
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 56, // Minimum 44pt touch target
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMd),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: _getButtonColor(type),
          foregroundColor: AppTheme.white,
          elevation: type == ButtonType.emergency ? 8 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusLg),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (type == ButtonType.emergency)
              Icon(Icons.warning, color: AppTheme.white),
            const SizedBox(width: AppTheme.spacingSm),
            Text(
              label,
              style: AppTheme.buttonLarge,
            ),
          ],
        ),
      ),
    );
  }
}
```

---

*This frontend architecture ensures the app meets the unique needs of electrical workers while maintaining Flutter best practices and performance standards.*
