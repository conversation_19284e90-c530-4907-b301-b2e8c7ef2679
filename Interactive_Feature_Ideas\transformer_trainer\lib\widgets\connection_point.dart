
import 'package:flutter/material.dart';
import '../models/transformer_models.dart';

/// Interactive connection point widget
class ConnectionPointWidget extends StatefulWidget {
  final ConnectionPoint connectionPoint;
  final bool isSelected;
  final bool isConnected;
  final bool showGuidance;
  final VoidCallback onTap;

  const ConnectionPointWidget({
    Key? key,
    required this.connectionPoint,
    required this.isSelected,
    required this.isConnected,
    required this.showGuidance,
    required this.onTap,
  }) : super(key: key);

  @override
  State<ConnectionPointWidget> createState() => _ConnectionPointWidgetState();
}

class _ConnectionPointWidgetState extends State<ConnectionPointWidget>
    with SingleTickerProviderStateMixin {
  AnimationController? _pulseController;
  Animation<double>? _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController!,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController?.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ConnectionPointWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Start pulse animation if selected
    if (widget.isSelected && !oldWidget.isSelected) {
      _pulseController?.repeat(reverse: true);
    } else if (!widget.isSelected && oldWidget.isSelected) {
      _pulseController?.stop();
      _pulseController?.reset();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _pulseAnimation ?? const AlwaysStoppedAnimation(1.0),
        builder: (context, child) {
          return Transform.scale(
            scale: widget.isSelected ? _pulseAnimation!.value : 1.0,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: _getConnectionPointColor(),
                border: Border.all(
                  color: _getConnectionPointBorderColor(),
                  width: 2.0,
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  if (widget.isSelected)
                    BoxShadow(
                      color: _getConnectionPointColor().withOpacity(0.5),
                      spreadRadius: 2,
                      blurRadius: 8,
                    ),
                ],
              ),
              child: Center(
                child: Icon(
                  _getConnectionPointIcon(),
                  size: 12,
                  color: Colors.white,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Get connection point color based on type and state
  Color _getConnectionPointColor() {
    if (widget.isConnected) {
      return Colors.green;
    }
    
    if (widget.isSelected) {
      return Colors.blue;
    }
    
    switch (widget.connectionPoint.type) {
      case ConnectionType.primary:
        return Colors.red[700]!;
      case ConnectionType.secondary:
        return Colors.blue[700]!;
      case ConnectionType.neutral:
        return Colors.grey[600]!;
      case ConnectionType.ground:
        return Colors.brown[700]!;
    }
  }

  /// Get connection point border color
  Color _getConnectionPointBorderColor() {
    if (widget.isSelected) {
      return Colors.blue[800]!;
    }
    
    if (widget.isConnected) {
      return Colors.green[800]!;
    }
    
    return Colors.grey[800]!;
  }

  /// Get appropriate icon for connection point
  IconData _getConnectionPointIcon() {
    if (widget.isConnected) {
      return Icons.link;
    }
    
    switch (widget.connectionPoint.type) {
      case ConnectionType.primary:
        return Icons.bolt;
      case ConnectionType.secondary:
        return Icons.power;
      case ConnectionType.neutral:
        return Icons.horizontal_rule;
      case ConnectionType.ground:
        return Icons.ground;
    }
  }
}

/// Tooltip widget for connection points
class ConnectionPointTooltip extends StatelessWidget {
  final ConnectionPoint connectionPoint;
  final Widget child;

  const ConnectionPointTooltip({
    Key? key,
    required this.connectionPoint,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: _getTooltipMessage(),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(8.0),
      ),
      textStyle: const TextStyle(
        color: Colors.white,
        fontSize: 12,
      ),
      child: child,
    );
  }

  String _getTooltipMessage() {
    final typeDescription = _getTypeDescription();
    final inputOutput = connectionPoint.isInput ? 'Input' : 'Output';
    
    return '${connectionPoint.label}\n$typeDescription $inputOutput';
  }

  String _getTypeDescription() {
    switch (connectionPoint.type) {
      case ConnectionType.primary:
        return 'Primary Side';
      case ConnectionType.secondary:
        return 'Secondary Side';
      case ConnectionType.neutral:
        return 'Neutral Point';
      case ConnectionType.ground:
        return 'Ground Connection';
    }
  }
}
