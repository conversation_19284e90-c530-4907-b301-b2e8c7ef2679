import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import '../painters/base_transformer_painter.dart';
import 'transformer_performance_monitor.dart';

/// Manages rendering optimizations for transformer diagrams
class RenderOptimizationManager {
  static final RenderOptimizationManager _instance = RenderOptimizationManager._();
  static RenderOptimizationManager get instance => _instance;
  
  RenderOptimizationManager._();
  
  // Picture caching for static backgrounds
  final Map<String, ui.Picture> _pictureCache = {};
  final Map<String, ui.Image> _rasterCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  
  // Render layer management
  final Map<String, RenderLayer> _renderLayers = {};
  
  // Configuration
  static const Duration cacheExpiration = Duration(minutes: 5);
  static const int maxCacheSize = 20;
  
  /// Create optimized render layers for transformer diagram
  RenderLayerSet createRenderLayers(String diagramType, Size size) {
    final monitor = TransformerPerformanceMonitor.instance;
    monitor.startOperation('diagram_render');
    
    try {
      // Create or retrieve render layers
      final backgroundLayer = _getOrCreateLayer(
        '${diagramType}_background',
        LayerType.static,
        size,
      );
      
      final connectionLayer = _getOrCreateLayer(
        '${diagramType}_connections',
        LayerType.dynamic,
        size,
      );
      
      final animationLayer = _getOrCreateLayer(
        '${diagramType}_animations',
        LayerType.animated,
        size,
      );
      
      monitor.endOperation('diagram_render', metadata: {
        'diagram_type': diagramType,
        'size': '${size.width}x${size.height}',
        'cached': _pictureCache.containsKey('${diagramType}_background'),
      });
      
      return RenderLayerSet(
        background: backgroundLayer,
        connections: connectionLayer,
        animations: animationLayer,
      );
    } catch (e) {
      monitor.endOperation('diagram_render', metadata: {
        'error': e.toString(),
      });
      rethrow;
    }
  }
  
  /// Get or create a render layer
  RenderLayer _getOrCreateLayer(String id, LayerType type, Size size) {
    final existing = _renderLayers[id];
    if (existing != null && existing.size == size) {
      return existing;
    }
    
    final layer = RenderLayer(
      id: id,
      type: type,
      size: size,
    );
    
    _renderLayers[id] = layer;
    return layer;
  }
  
  /// Cache static background as picture
  Future<ui.Picture?> cacheStaticBackground(
    String cacheKey,
    Size size,
    void Function(Canvas) paintCallback,
  ) async {
    // Check if cached and not expired
    final cachedPicture = _pictureCache[cacheKey];
    final timestamp = _cacheTimestamps[cacheKey];
    
    if (cachedPicture != null && 
        timestamp != null &&
        DateTime.now().difference(timestamp) < cacheExpiration) {
      return cachedPicture;
    }
    
    // Create new picture
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    
    // Execute paint operations
    paintCallback(canvas);
    
    // End recording
    final picture = recorder.endRecording();
    
    // Cache the picture
    _pictureCache[cacheKey] = picture;
    _cacheTimestamps[cacheKey] = DateTime.now();
    
    // Manage cache size
    _cleanupCache();
    
    return picture;
  }
  
  /// Rasterize picture for better performance
  Future<ui.Image?> rasterizePicture(
    String cacheKey,
    ui.Picture picture,
    Size size,
  ) async {
    // Check raster cache
    final cached = _rasterCache[cacheKey];
    if (cached != null) {
      return cached;
    }
    
    try {
      // Convert picture to image
      final image = await picture.toImage(
        size.width.toInt(),
        size.height.toInt(),
      );
      
      // Cache the rasterized image
      _rasterCache[cacheKey] = image;
      
      return image;
    } catch (e) {
      debugPrint('Failed to rasterize picture: $e');
      return null;
    }
  }
  
  /// Create optimized paint object
  Paint createOptimizedPaint({
    Color color = Colors.black,
    double strokeWidth = 2.0,
    PaintingStyle style = PaintingStyle.stroke,
    StrokeCap strokeCap = StrokeCap.round,
    StrokeJoin strokeJoin = StrokeJoin.round,
    bool antiAlias = true,
  }) {
    return Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = style
      ..strokeCap = strokeCap
      ..strokeJoin = strokeJoin
      ..isAntiAlias = antiAlias
      ..filterQuality = FilterQuality.medium; // Balance quality and performance
  }
  
  /// Optimize path for rendering
  Path optimizePath(Path originalPath) {
    // Simplify path if too complex
    final metrics = originalPath.computeMetrics();
    int pointCount = 0;
    
    for (final metric in metrics) {
      pointCount += metric.length.toInt();
    }
    
    // If path is too complex, simplify it
    if (pointCount > 1000) {
      return _simplifyPath(originalPath);
    }
    
    return originalPath;
  }
  
  /// Simplify complex paths
  Path _simplifyPath(Path path) {
    // Douglas-Peucker algorithm for path simplification
    // This is a simplified implementation
    final simplified = Path();
    final metrics = path.computeMetrics();
    
    for (final metric in metrics) {
      final length = metric.length;
      final step = length / 100; // Sample 100 points max
      
      ui.Tangent? lastTangent;
      for (double distance = 0; distance <= length; distance += step) {
        final tangent = metric.getTangentForOffset(distance);
        if (tangent != null) {
          if (lastTangent == null) {
            simplified.moveTo(tangent.position.dx, tangent.position.dy);
          } else {
            simplified.lineTo(tangent.position.dx, tangent.position.dy);
          }
          lastTangent = tangent;
        }
      }
    }
    
    return simplified;
  }
  
  /// Clean up expired cache entries
  void _cleanupCache() {
    if (_pictureCache.length <= maxCacheSize) return;
    
    // Find oldest entries
    final entries = _cacheTimestamps.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    // Remove oldest entries
    final toRemove = entries.take(entries.length - maxCacheSize);
    for (final entry in toRemove) {
      _pictureCache.remove(entry.key);
      _rasterCache[entry.key]?.dispose();
      _rasterCache.remove(entry.key);
      _cacheTimestamps.remove(entry.key);
    }
  }
  
  /// Batch render operations for better performance
  void batchRenderOperations(List<RenderOperation> operations) {
    // Sort operations by priority
    operations.sort((a, b) => b.priority.compareTo(a.priority));
    
    // Execute in batches
    const batchSize = 5;
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations.skip(i).take(batchSize);
      
      // Execute batch
      for (final operation in batch) {
        operation.execute();
      }
      
      // Allow frame to render between batches
      WidgetsBinding.instance.scheduleFrame();
    }
  }
  
  /// Clear all caches
  void clearCache() {
    _pictureCache.clear();
    for (final image in _rasterCache.values) {
      image.dispose();
    }
    _rasterCache.clear();
    _cacheTimestamps.clear();
    _renderLayers.clear();
  }
  
  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'picture_cache_size': _pictureCache.length,
      'raster_cache_size': _rasterCache.length,
      'render_layers': _renderLayers.length,
      'oldest_cache': _cacheTimestamps.values.isEmpty 
          ? 'none' 
          : _cacheTimestamps.values.reduce((a, b) => a.isBefore(b) ? a : b).toIso8601String(),
    };
  }
}

/// Render layer for organizing drawing operations
class RenderLayer {
  final String id;
  final LayerType type;
  final Size size;
  ui.Picture? cachedPicture;
  ui.Image? cachedImage;
  bool isDirty = true;
  
  RenderLayer({
    required this.id,
    required this.type,
    required this.size,
  });
  
  void markDirty() {
    isDirty = true;
  }
  
  void markClean() {
    isDirty = false;
  }
}

/// Types of render layers
enum LayerType {
  static,   // Background, rarely changes
  dynamic,  // Connections, changes occasionally
  animated, // Animations, changes frequently
}

/// Set of render layers for a diagram
class RenderLayerSet {
  final RenderLayer background;
  final RenderLayer connections;
  final RenderLayer animations;
  
  RenderLayerSet({
    required this.background,
    required this.connections,
    required this.animations,
  });
}

/// Render operation for batching
class RenderOperation {
  final VoidCallback execute;
  final int priority;
  
  RenderOperation({
    required this.execute,
    this.priority = 0,
  });
}