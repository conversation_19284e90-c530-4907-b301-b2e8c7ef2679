# Code Structure & Conventions

## Project Architecture
Feature-based architecture following Flutter best practices:

```
lib/
├── screens/                 # Screen widgets (home/, jobs/, unions/, etc.)
├── widgets/                 # Reusable components
├── services/                # Business logic and API calls
├── providers/               # State management (Provider pattern)
├── models/                  # Data models
├── design_system/           # Theme and design components
├── electrical_components/   # Electrical-themed UI components
├── navigation/              # Router configuration
└── utils/                   # Helper utilities
```

## Naming Conventions
- **Screens**: `{feature}_screen.dart` (e.g., `jobs_screen.dart`)
- **Widgets**: `{component}_widget.dart` or specific names
- **Services**: `{feature}_service.dart` (e.g., `auth_service.dart`)
- **Models**: `{entity}_model.dart` (e.g., `job_model.dart`)
- **Providers**: `{feature}_provider.dart`

## Import Standards
- Relative imports within same feature
- Absolute imports for cross-feature dependencies
- Material/Cupertino imports first, then package imports, then local imports

## Code Style
- Null safety enabled
- Strong typing throughout
- Comprehensive documentation with dartdoc comments
- Error handling with try-catch blocks
- Async/await for asynchronous operations