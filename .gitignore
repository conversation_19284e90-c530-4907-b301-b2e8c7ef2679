# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Environment variables and secrets
.env
.env.local
.env.production
.env.staging
*.env
.mcp.json.backup
.mcp.json
.API_KEY_ROTATION_REQUIRED
# npm cache (should never be in project)
# npm cache (should never be in project)
# npm cache (should never be in project)
# npm cache (should never be in project)

# npm cache (should never be in project)
C:\\Users\\<USER>\\AppData\\Roaming\\npm-cache/
