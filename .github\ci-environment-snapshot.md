# CI Environment Snapshot

Purpose: Record current CI configuration related to Java/JDK and Gradle to support safe upgrades and easy rollback/context.

Date: 2025-08-10
Branch: chore/agp-8-6-upgrade

Detected CI configuration files:
- .github/workflows/ci.yml

Key settings observed in .github/workflows/ci.yml:
- env.JAVA_VERSION: '17'
  - Used by actions/setup-java@v3 with distribution: 'zulu' in Android build job
- env.FLUTTER_VERSION: '3.16.0'
  - Used by subosito/flutter-action@v2 across jobs

Gradle-specific notes for Android build job:
- Java setup step:
  - uses: actions/setup-java@v3
  - with:
    - distribution: zulu
    - java-version: ${{ env.JAVA_VERSION }} (currently 17)
- No explicit Gradle wrapper version set here; Gradle version is resolved from the repository's gradle-wrapper.properties.

Other CI systems checked:
- CircleCI: not found
- GitLab CI: not found
- Azure Pipelines: not found
- Jenkins: not found
- Travis CI: not found
- Drone CI: not found
- Buildkite: not found

Repository files to be tracked in this upgrade commit:
- android/gradle/wrapper/gradle-wrapper.properties (Gradle Wrapper version/source)
- android/build.gradle (Top-level Gradle build config)
- android/settings.gradle (Project settings and plugin management)
- android/app/build.gradle (App module build config)
- android/gradle.properties (Gradle properties for Android project)
- buildSrc: not detected
- gradle/libs.versions.toml (versions catalog): not detected

Rationale:
- Capturing current CI environment and Gradle config ensures we can compare pre/post-upgrade behavior and roll back if necessary.

