# Transformer Bank Feature - UI/UX Design Specifications

## Overview

This document outlines the complete user interface and user experience design for the Transformer Bank feature, which serves as both a reference tool and interactive training system for IBEW electrical workers. The feature integrates seamlessly with the existing app's electrical theme and design system.

## 1. Feature Integration & Navigation

### 1.1 Settings Screen Integration

**Location**: Add to the "Support" section in `/lib/screens/settings/settings_screen.dart`

**New Menu Item**:

```dart
_MenuOption(
  icon: Icons.transform,
  title: 'Transformer Bank Trainer',
  subtitle: 'Interactive transformer training & reference',
  onTap: () => context.push(AppRouter.transformerBank),
),
```

**Visual Design**:

- Icon: `Icons.transform` with copper accent color
- Follows existing menu item styling with electrical theme
- Subtitle clearly indicates both training and reference capabilities

### 1.2 App Router Integration

**Route**: `/transformer-bank`

```dart
GoRoute(
  path: '/transformer-bank',
  name: AppRouter.transformerBank,
  builder: (context, state) => const TransformerBankHomeScreen(),
),
```

## 2. Screen Architecture

### 2.1 Transformer Bank Home Screen (New)

**Purpose**: Landing page for selecting between Reference and Training modes

**Layout**:

``` wireframe
┌─────────────────────────────────────┐
│ AppBar: "Transformer Bank"          │
├─────────────────────────────────────┤
│ Hero Section                        │
│ ┌─────────────────────────────────┐ │
│ │ Circuit Pattern Background      │ │
│ │ Electrical Animation            │ │
│ │ "Master Transformer Banks"      │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Mode Selection Cards                │
│ ┌─────────────┐ ┌─────────────────┐ │
│ │ REFERENCE   │ │   TRAINING      │ │
│ │ Mode        │ │   Mode          │ │
│ │ [Icon]      │ │   [Icon]        │ │
│ │ Study &     │ │   Test Your     │ │
│ │ Learn       │ │   Knowledge     │ │
│ └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│ Quick Access (Optional)             │
│ • Recent Training Sessions          │
│ • Bookmarked Configurations         │
└─────────────────────────────────────┘
```

**Component Specifications**:

```dart
class TransformerBankHomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightGray,
      appBar: AppBar(
        title: Text(
          'Transformer Bank',
          style: AppTheme.headlineMedium.copyWith(color: AppTheme.white),
        ),
        backgroundColor: AppTheme.primaryNavy,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeroSection(),
            _buildModeSelection(context),
            _buildQuickAccess(),
          ],
        ),
      ),
    );
  }
}
```

### 2.2 Reference Mode Screen Architecture

**Navigation Flow**:

```flow
Transformer Bank Home → Reference Mode → Configuration Selection → Interactive Diagram
```

**Configuration Selection Screen**:

``` wireframe
┌─────────────────────────────────────┐
│ AppBar: "Reference Mode"            │
├─────────────────────────────────────┤
│ Instructions Card                   │
│ "Tap any transformer configuration  │
│  to explore its components and      │
│  connections."                      │
├─────────────────────────────────────┤
│ Configuration Grid                  │
│ ┌──────────┐ ┌──────────┐ ┌────────┐│
│ │Single Pot│ │Open Delta│ │Wye-Wye ││
│ │120V/240V │ │2-Pot Bank│ │3-Pot   ││
│ │[Diagram] │ │[Diagram] │ │[Diagram│││
│ └──────────┘ └──────────┘ └────────┘│
│ ┌──────────┐ ┌──────────┐ ┌────────┐│
│ │Delta-    │ │Wye-Delta │ │Delta-  ││
│ │Delta     │ │3-Pot Bank│ │Wye     ││
│ │[Diagram] │ │[Diagram] │ │[Diagram│││
│ └──────────┘ └──────────┘ └────────┘│
└─────────────────────────────────────┘
```

**Interactive Diagram Screen**:

``` wireframe
┌─────────────────────────────────────┐
│ AppBar: "[Configuration Name]"      │
│ Actions: [Info] [Share]             │
├─────────────────────────────────────┤
│ Transformer Diagram Canvas          │
│ ┌─────────────────────────────────┐ │
│ │ Interactive SVG/Custom Paint    │ │
│ │ - Hover highlights components   │ │
│ │ - Tap shows component info      │ │
│ │ - Zoom/pan support              │ │
│ │ - Connection labels visible     │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Information Panel (Expandable)      │
│ ┌─────────────────────────────────┐ │
│ │ Component: "H1 Primary Bushing" │ │
│ │ Description: [Technical info]   │ │
│ │ Safety Notes: [Important info]  │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 2.3 Training Mode Screen Architecture

**Enhanced Existing Implementation**:

The existing transformer trainer in `/lib/electrical_components/transformer_trainer/jj_transformer_trainer.dart` provides the foundation. Enhancements needed:

```dart
class EnhancedTransformerTrainer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildDifficultyIndicator(), // NEW: Visual difficulty indicator
        _buildControlPanel(),        // ENHANCED: Better visual hierarchy
        Expanded(
          child: _buildMainContent(), // ENHANCED: Reference vs Training modes
        ),
        _buildProgressIndicator(),   // NEW: Progress tracking
      ],
    );
  }
}
```

## 3. Difficulty Level Visual Differentiation

### 3.1 Color Schemes by Difficulty

**Beginner (120V/240V Residential)**:

- Primary: Soft Green (#34D399)
- Secondary: Light Green Background (#ECFDF5)
- Accent: Emerald (#059669)
- Animation: Gentle, slow pulsing
- Complexity: Large components, clear labels

**Intermediate (240V/480V Commercial)**:

- Primary: Copper/Orange (#F59E0B)  
- Secondary: Light Orange Background (#FFFBEB)
- Accent: Amber (#D97706)
- Animation: Moderate speed, subtle flash effects
- Complexity: Medium components, technical labels

**Advanced (480V+ Industrial)**:

- Primary: Deep Red (#DC2626)
- Secondary: Light Red Background (#FEF2F2)
- Accent: Dark Red (#991B1B)
- Animation: Fast, intense electrical effects
- Complexity: Detailed components, professional terminology

### 3.2 Difficulty Indicator Component

```dart
class DifficultyIndicator extends StatelessWidget {
  final DifficultyLevel level;
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        gradient: _getDifficultyGradient(level),
        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
        boxShadow: [AppTheme.shadowSm],
      ),
      child: Row(
        children: [
          _buildDifficultyIcon(level),
          SizedBox(width: AppTheme.spacingSm),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getDifficultyTitle(level),
                style: AppTheme.titleMedium.copyWith(
                  color: AppTheme.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                _getDifficultyDescription(level),
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
          Spacer(),
          _buildDifficultyStars(level),
        ],
      ),
    );
  }
}
```

## 4. Animation Specifications

### 4.1 Success Animations

**Correct Connection Animation**:

- Green pulse from connection point
- Sparkling particle effect
- Gentle power-up glow on transformer
- Success sound (optional vibration)
- Duration: 2 seconds

**Implementation**:

```dart
class SuccessAnimation extends StatefulWidget {
  final VoidCallback onComplete;
  
  @override
  State<SuccessAnimation> createState() => _SuccessAnimationState();
}

class _SuccessAnimationState extends State<SuccessAnimation>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _sparkleController;
  late AnimationController _glowController;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSuccessSequence();
  }
  
  void _startSuccessSequence() async {
    await _pulseController.forward();
    _sparkleController.forward();
    await _glowController.forward();
    widget.onComplete();
  }
}
```

### 4.2 Error Animations

**Incorrect Connection Animation**:

- Red flash from connection point
- Electrical fire/spark effect (safe visual representation)
- Shake/vibration of transformer
- Warning sound (optional haptic feedback)
- Duration: 3 seconds

**Electrical Fire Effect**:

```dart
class ElectricalFireAnimation extends StatefulWidget {
  final Offset position;
  final VoidCallback onComplete;
  
  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: ElectricalFirePainter(
        animation: _fireAnimation,
        position: position,
      ),
    );
  }
}

class ElectricalFirePainter extends CustomPainter {
  final Animation<double> animation;
  final Offset position;
  
  @override
  void paint(Canvas canvas, Size size) {
    // Draw animated fire/sparks effect
    // Use red/orange/yellow gradient
    // Particle system for sparks
    // Fade out over time
  }
}
```

### 4.3 Difficulty Level Animations

**Beginner**:

- Slow, gentle highlighting (1.5s cycles)
- Soft glow effects
- Minimal particle effects

**Intermediate**:

- Moderate speed highlighting (1.0s cycles)
- Medium intensity effects
- Copper-colored electrical arcs

**Advanced**:

- Fast, intense highlighting (0.5s cycles)
- High-energy electrical effects
- Industrial-grade visual feedback

## 5. Drag & Drop Interface Specifications

### 5.1 Wire Connection System

**Sticky Keys Mode** (Default for mobile):

1. User taps a wire - wire highlights with selection color
2. User taps target connection point - connection attempt
3. Visual feedback for valid/invalid connections
4. Selected wire follows finger with ghost trail

**Drag & Drop Mode** (Alternative):

1. User long-presses wire to start drag
2. Wire follows finger with physics simulation
3. Drop zones highlight when wire is near
4. Snap-to-point when close enough

```dart
class WireConnectionWidget extends StatefulWidget {
  final ConnectionPoint startPoint;
  final ConnectionPoint? endPoint;
  final bool isSelected;
  final ConnectionMode mode;
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: mode == ConnectionMode.stickyKeys ? _handleTap : null,
      onLongPress: mode == ConnectionMode.dragDrop ? _startDrag : null,
      onPanUpdate: mode == ConnectionMode.dragDrop ? _updateDrag : null,
      onPanEnd: mode == ConnectionMode.dragDrop ? _endDrag : null,
      child: CustomPaint(
        painter: WirePainter(
          startPoint: startPoint,
          endPoint: endPoint,
          isSelected: isSelected,
          isDragging: _isDragging,
          dragPosition: _dragPosition,
        ),
      ),
    );
  }
}
```

### 5.2 Connection Points

**Visual Design**:

- Circular connection points with copper borders
- Glow effect when wire is selected/nearby
- Label overlay on hover/tap
- Different sizes for primary/secondary connections

```dart
class ConnectionPoint extends StatelessWidget {
  final String id;
  final String label;
  final ConnectionType type;
  final bool isHighlighted;
  final bool isConnected;
  final VoidCallback? onTap;
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: _getPointSize(type),
        height: _getPointSize(type),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: _getPointColor(type, isConnected),
          border: Border.all(
            color: isHighlighted ? AppTheme.accentCopper : AppTheme.mediumGray,
            width: isHighlighted ? 3 : 2,
          ),
          boxShadow: isHighlighted ? [AppTheme.shadowMd] : null,
        ),
        child: Center(
          child: Text(
            label,
            style: AppTheme.labelSmall.copyWith(
              color: AppTheme.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
```

## 6. Responsive Design Specifications

### 6.1 Phone Layout (Primary Target)

**Portrait Mode (< 600dp width)**:

- Single column layout
- Full-width transformer diagram
- Stacked control panels
- Scrollable content areas
- Larger touch targets (min 48dp)

**Landscape Mode**:

- Side-by-side layout where possible
- Diagram takes 60% width
- Controls panel 40% width
- Horizontal scrolling for configurations

### 6.2 Tablet Layout (Secondary)

**Enhanced Layout (≥ 600dp width)**:

- Two-column layout
- Larger diagram canvas
- Side panel for controls and information
- Multi-touch support for complex interactions

```dart
class ResponsiveTransformerLayout extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 600) {
          return _buildPhoneLayout();
        } else {
          return _buildTabletLayout();
        }
      },
    );
  }
  
  Widget _buildPhoneLayout() {
    return Column(
      children: [
        _buildControlPanel(),
        Expanded(child: _buildDiagram()),
      ],
    );
  }
  
  Widget _buildTabletLayout() {
    return Row(
      children: [
        SizedBox(
          width: 300,
          child: _buildControlPanel(),
        ),
        Expanded(child: _buildDiagram()),
      ],
    );
  }
}
```

## 7. Accessibility Specifications

### 7.1 Screen Reader Support

**Semantic Labels**:

- All connection points have descriptive labels
- Diagram components have semantic descriptions
- State changes are announced
- Instructions read aloud when screen changes

```dart
class AccessibleConnectionPoint extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: 'Connection point ${widget.label}, ${widget.type.name}',
      hint: isConnected ? 'Connected' : 'Available for connection',
      button: true,
      onTap: widget.onTap,
      child: ConnectionPoint(...),
    );
  }
}
```

### 7.2 Alternative Interaction Methods

**Voice Navigation** (Future Enhancement):

- "Connect H1 to X1"
- "Show Delta-Delta configuration"
- "Start beginner training"

**High Contrast Mode**:

- Increased color contrast ratios
- Thicker lines and borders
- Alternative color schemes for color-blind users

### 7.3 Haptic Feedback

**Connection Events**:

- Light haptic on wire selection
- Medium haptic on successful connection
- Strong haptic on error/wrong connection
- Subtle haptic on UI element highlights

## 8. Performance Optimization

### 8.1 Animation Performance

**Efficient Rendering**:

- Use `RepaintBoundary` for transformer diagrams
- Implement custom painters for complex drawings
- Limit concurrent animations
- Optimize particle systems for lower-end devices

```dart
class OptimizedTransformerDiagram extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: CustomPaint(
        painter: TransformerPainter(
          // Optimized painting logic
        ),
        size: Size.infinite,
      ),
    );
  }
}
```

### 8.2 Memory Management

**Caching Strategy**:

- Cache transformer diagram paths
- Preload next difficulty level assets
- Dispose animation controllers properly
- Use object pooling for particles

## 9. User Flow Diagrams

### 9.1 Reference Mode Flow

``` flow
Settings → Transformer Bank Home → Reference Mode
    ↓
Configuration Selection Grid
    ↓
Interactive Diagram View
    ↓
Component Information Panel
    ↓
[User can navigate back or switch to Training]
```

### 9.2 Training Mode Flow

``` flow
Settings → Transformer Bank Home → Training Mode
    ↓
Difficulty Selection
    ↓
Configuration Selection
    ↓
Interactive Training Session
    ↓
Success/Error Feedback
    ↓
Progress Tracking & Next Steps
```

### 9.3 Cross-Mode Navigation

**Quick Switch Feature**:

- Users can switch between Reference and Training modes without losing context
- "Switch to Training" button in Reference mode
- "View Reference" button in Training mode
- Maintains current configuration selection

## 10. Testing & Validation Requirements

### 10.1 Usability Testing Scenarios

**Reference Mode**:

1. New user explores Wye-Wye configuration
2. Experienced user looks up specific component information
3. User switches between multiple configurations quickly

**Training Mode**:

1. Beginner completes first training session
2. Intermediate user makes deliberate mistakes
3. Advanced user completes complex configuration under time pressure

### 10.2 Accessibility Testing

**Screen Reader Navigation**:

- Complete training session using only screen reader
- Navigation between components using keyboard/switch control
- Information comprehension without visual elements

### 10.3 Performance Testing

**Animation Stress Tests**:

- Multiple simultaneous connections
- Rapid difficulty level switching
- Extended training sessions (memory leaks)
- Background/foreground app transitions

## 11. Implementation Priority

### Phase 1 (MVP)

1. ✅ Basic transformer trainer (already implemented)
2. 🆕 Reference mode with static diagrams
3. 🆕 Difficulty level visual differentiation
4. 🆕 Basic success/error animations

### Phase 2 (Enhanced)

1. 🆕 Drag & drop interface
2. 🆕 Interactive component information
3. 🆕 Progress tracking
4. 🆕 Quick mode switching

### Phase 3 (Advanced)

1. 🆕 Voice navigation
2. 🆕 Haptic feedback
3. 🆕 Performance optimizations
4. 🆕 Advanced accessibility features

---

## Conclusion

This design specification provides a comprehensive blueprint for implementing the Transformer Bank feature that seamlessly integrates with the existing Journeyman Jobs app architecture while providing an engaging, educational, and accessible experience for IBEW electrical workers at all skill levels.

The design leverages the existing electrical theme, maintains consistency with current UI patterns, and introduces innovative training methodologies that will help electricians master transformer bank configurations safely and effectively.
