import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../lib/electrical_components/transformer_trainer/widgets/connection_point.dart';
import '../../../../../lib/electrical_components/transformer_trainer/models/transformer_models.dart';
import '../../../../../lib/design_system/app_theme.dart';

void main() {
  group('ConnectionPointWidget Tests', () {
    late ConnectionPoint testConnectionPoint;

    setUp(() {
      testConnectionPoint = ConnectionPoint(
        id: 'test_1',
        label: 'H1',
        type: ConnectionType.primary,
        position: const Offset(100, 100),
        isInput: true,
      );
    });

    testWidgets('renders connection point correctly', (tester) async {
      bool wasTapped = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: ConnectionPointWidget(
              connectionPoint: testConnectionPoint,
              isSelected: false,
              isConnected: false,
              showGuidance: false,
              isCompatible: false,
              isDragSource: false,
              connectionMode: ConnectionMode.stickyKeys,
              onTap: () => wasTapped = true,
            ),
          ),
        ),
      );

      // Verify widget renders
      expect(find.byType(ConnectionPointWidget), findsOneWidget);
      
      // Verify icon is present
      expect(find.byType(Icon), findsOneWidget);
      
      // Test tap functionality
      await tester.tap(find.byType(ConnectionPointWidget));
      await tester.pumpAndSettle();
      
      expect(wasTapped, isTrue);
    });

    testWidgets('displays correct colors for different connection types', (tester) async {
      // Test primary connection type
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ConnectionPointWidget(
              connectionPoint: testConnectionPoint,
              isSelected: false,
              isConnected: false,
              showGuidance: false,
              isCompatible: false,
              isDragSource: false,
              connectionMode: ConnectionMode.stickyKeys,
              onTap: () {},
            ),
          ),
        ),
      );

      expect(find.byType(ConnectionPointWidget), findsOneWidget);
      
      // Widget should use AppTheme colors (exact color testing would require
      // accessing the widget's decoration, which is complex in widget tests)
    });

    testWidgets('handles selected state correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ConnectionPointWidget(
              connectionPoint: testConnectionPoint,
              isSelected: true, // Selected state
              isConnected: false,
              showGuidance: false,
              isCompatible: false,
              isDragSource: false,
              connectionMode: ConnectionMode.stickyKeys,
              onTap: () {},
            ),
          ),
        ),
      );

      expect(find.byType(ConnectionPointWidget), findsOneWidget);
      
      // In selected state, the widget should show visual feedback
      // This would be verified through animation or visual state checks
    });

    testWidgets('handles connected state correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ConnectionPointWidget(
              connectionPoint: testConnectionPoint,
              isSelected: false,
              isConnected: true, // Connected state
              showGuidance: false,
              isCompatible: false,
              isDragSource: false,
              connectionMode: ConnectionMode.stickyKeys,
              onTap: () {},
            ),
          ),
        ),
      );

      expect(find.byType(ConnectionPointWidget), findsOneWidget);
      
      // Connected state should show success colors (AppTheme.successGreen)
    });

    testWidgets('handles compatible state with glow animation', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ConnectionPointWidget(
              connectionPoint: testConnectionPoint,
              isSelected: false,
              isConnected: false,
              showGuidance: false,
              isCompatible: true, // Compatible state
              isDragSource: false,
              connectionMode: ConnectionMode.stickyKeys,
              onTap: () {},
            ),
          ),
        ),
      );

      expect(find.byType(ConnectionPointWidget), findsOneWidget);
      
      // Allow animations to start
      await tester.pump(const Duration(milliseconds: 100));
      
      // Compatible state should trigger glow animation
    });

    testWidgets('handles drag and drop mode correctly', (tester) async {
      String? droppedData;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ConnectionPointWidget(
              connectionPoint: testConnectionPoint,
              isSelected: false,
              isConnected: false,
              showGuidance: false,
              isCompatible: false,
              isDragSource: false,
              connectionMode: ConnectionMode.dragDrop, // Drag-drop mode
              onTap: () {},
              onAcceptDrop: (data) => droppedData = data,
            ),
          ),
        ),
      );

      expect(find.byType(ConnectionPointWidget), findsOneWidget);
      expect(find.byType(LongPressDraggable<String>), findsOneWidget);
      expect(find.byType(DragTarget<String>), findsOneWidget);
    });

    testWidgets('shows appropriate icons for different connection types', (tester) async {
      final connectionTypes = [
        (ConnectionType.primary, Icons.bolt),
        (ConnectionType.secondary, Icons.power),
        (ConnectionType.neutral, Icons.horizontal_rule),
        (ConnectionType.ground, Icons.electrical_services),
      ];

      for (final (type, expectedIcon) in connectionTypes) {
        final connectionPoint = ConnectionPoint(
          id: 'test_${type.name}',
          label: type.name,
          type: type,
          position: const Offset(100, 100),
          isInput: true,
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ConnectionPointWidget(
                connectionPoint: connectionPoint,
                isSelected: false,
                isConnected: false,
                showGuidance: false,
                isCompatible: false,
                isDragSource: false,
                connectionMode: ConnectionMode.stickyKeys,
                onTap: () {},
              ),
            ),
          ),
        );

        expect(find.byType(ConnectionPointWidget), findsOneWidget);
        expect(find.byIcon(expectedIcon), findsOneWidget);
      }
    });

    testWidgets('handles press feedback with scale animation', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ConnectionPointWidget(
              connectionPoint: testConnectionPoint,
              isSelected: false,
              isConnected: false,
              showGuidance: false,
              isCompatible: false,
              isDragSource: false,
              connectionMode: ConnectionMode.stickyKeys,
              onTap: () {},
            ),
          ),
        ),
      );

      // Test tap down (should trigger scale animation)
      await tester.press(find.byType(ConnectionPointWidget));
      await tester.pump(const Duration(milliseconds: 50));
      
      // Widget should be in pressed state with scale animation
      expect(find.byType(ConnectionPointWidget), findsOneWidget);
      
      // Release the press
      await tester.pumpAndSettle();
    });

    testWidgets('respects mobile touch target size', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ConnectionPointWidget(
              connectionPoint: testConnectionPoint,
              isSelected: false,
              isConnected: false,
              showGuidance: false,
              isCompatible: false,
              isDragSource: false,
              connectionMode: ConnectionMode.stickyKeys,
              onTap: () {},
            ),
          ),
        ),
      );

      // Find the touch target container
      expect(find.byType(ConnectionPointWidget), findsOneWidget);
      
      // The widget should have adequate touch target size (44x44 minimum)
      // This ensures accessibility compliance
    });
  });

  group('ConnectionPointTooltip Tests', () {
    late ConnectionPoint testConnectionPoint;

    setUp(() {
      testConnectionPoint = ConnectionPoint(
        id: 'tooltip_test',
        label: 'H1',
        type: ConnectionType.primary,
        position: const Offset(100, 100),
        isInput: true,
      );
    });

    testWidgets('displays correct tooltip message', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ConnectionPointTooltip(
              connectionPoint: testConnectionPoint,
              child: Container(
                width: 50,
                height: 50,
                color: Colors.blue,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(ConnectionPointTooltip), findsOneWidget);
      expect(find.byType(Tooltip), findsOneWidget);
      
      // Test tooltip trigger (long press or hover)
      await tester.longPress(find.byType(Container));
      await tester.pumpAndSettle();
      
      // Tooltip should contain the connection point label and type description
      expect(find.text('H1'), findsOneWidget);
    });

    testWidgets('shows correct type descriptions', (tester) async {
      final testCases = [
        (ConnectionType.primary, 'Primary Side'),
        (ConnectionType.secondary, 'Secondary Side'),
        (ConnectionType.neutral, 'Neutral Point'),
        (ConnectionType.ground, 'Ground Connection'),
      ];

      for (final (type, expectedDescription) in testCases) {
        final connectionPoint = ConnectionPoint(
          id: 'tooltip_${type.name}',
          label: 'Test',
          type: type,
          position: const Offset(100, 100),
          isInput: true,
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ConnectionPointTooltip(
                connectionPoint: connectionPoint,
                child: Container(
                  width: 50,
                  height: 50,
                  color: Colors.blue,
                ),
              ),
            ),
          ),
        );

        // The tooltip message should contain the expected description
        // This would be verified through the tooltip's message property
        expect(find.byType(ConnectionPointTooltip), findsOneWidget);
      }
    });
  });
}